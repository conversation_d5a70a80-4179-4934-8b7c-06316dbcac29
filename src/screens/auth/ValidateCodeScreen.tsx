import React, { useState, useEffect } from 'react';
import {View, TextInput, TouchableOpacity, ActivityIndicator, KeyboardAvoidingView, Platform, Text, ScrollView} from 'react-native';
import { BlurView } from 'expo-blur';
import { verifyEmailCode, resendEmailVerification, resendPasswordResetEmailCode, verifyPasswordResetEmailCode } from '../../services/emailVerificationService';
import { verifyPhoneCode, resendPhoneVerification, resendPasswordResetCode, verifyPasswordResetCode } from '../../services/phoneVerificationService';
import BaseScreen from '../../components/BaseScreen';
import Title from '../../components/common/Title';
import { vs } from 'react-native-size-matters';
import { forgotPasswordStyles as styles } from '../../styles/forgotPasswordStyles';
import { showApiErrorAlert, showSuccessAlert, showPVCErrorAlert, showValidationAlert } from '../../utils/alertUtils';
import LogoTS from "../../components/svg/LogoTS";
import {globalTheme} from "../../constants/theme";

export default function ValidateCodeScreen({ navigation, route }: any) {
    const [code, setCode] = useState('');
    const [loading, setLoading] = useState(false);
    const [resendTimer, setResendTimer] = useState(0);
    const [canResend, setCanResend] = useState(true);
    const [isNavigating, setIsNavigating] = useState(false);
    const { method, userId, email, phone, isPasswordReset } = route.params;

    console.log("[ValidateCode] Params recibidos:", {
        method,
        userId,
        email,
        phone,
        isPasswordReset,
        typeOfUserId: typeof userId
    });

    // Timer para el botón de reenviar código
    useEffect(() => {
        let interval: NodeJS.Timeout | null = null;

        if (resendTimer > 0) {
            interval = setInterval(() => {
                setResendTimer((prev) => {
                    if (prev <= 1) {
                        setCanResend(true);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        // Cleanup function - CRÍTICO para evitar memory leaks
        return () => {
            if (interval) {
                clearInterval(interval);
                interval = null;
            }
        };
    }, [resendTimer]);

    // Cleanup al desmontar el componente
    useEffect(() => {
        return () => {
            // Limpiar cualquier timer activo al desmontar
            setResendTimer(0);
            setCanResend(true);
            setLoading(false);
            setIsNavigating(false);
        };
    }, []);

    // Función para iniciar el timer de reenvío
    const startResendTimer = () => {
        setResendTimer(90); // 1:30 = 90 segundos
        setCanResend(false);
    };

    // Función para formatear el tiempo del timer
    const formatTimer = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const handleVerify = async () => {
        if (isNavigating || loading) {
            return; // Prevenir múltiples llamadas
        }

        if (!userId) {
            showApiErrorAlert('Error: ID de usuario no válido');
            return;
        }

        if (!code.trim()) {
            showValidationAlert('code', 'Por favor ingresa el código de verificación.');
            return;
        }

        setLoading(true);
        try {
            console.log("[ValidateCode] Intentando verificar código:", {
                method,
                userId,
                code: code.trim(),
                isPasswordReset
            });

            let result;

            // Determinar qué función usar basado en el contexto
            if (isPasswordReset) {
                // Recuperación de contraseña - usar endpoints específicos
                console.log('[ValidateCode] Verificando código de recuperación de contraseña');
                result = method === 'email'
                    ? await verifyPasswordResetEmailCode(userId, code.trim())
                    : await verifyPasswordResetCode(userId, code.trim());
            } else {
                // Verificación de registro - usar endpoints de verificación
                console.log('[ValidateCode] Verificando código de registro');
                result = method === 'email'
                    ? await verifyEmailCode(userId, code.trim())
                    : await verifyPhoneCode(userId, code.trim());
            }

            console.log("[ValidateCode] Resultado de verificación:", result);

            if (result.success) {
                console.log("[ValidateCode] Verificación exitosa, mostrando alerta y preparando navegación");

                showSuccessAlert(
                    '✅ Verificado',
                    result.alreadyVerified
                        ? isPasswordReset
                            ? 'Tu cuenta ya estaba verificada. Ahora puedes cambiar tu contraseña.'
                            : 'Tu cuenta ya estaba verificada. ¡Bienvenido!'
                        : isPasswordReset
                            ? method === 'email'
                                ? '¡Correo electrónico verificado! Ahora puedes cambiar tu contraseña.'
                                : '¡Teléfono verificado! Ahora puedes cambiar tu contraseña.'
                            : method === 'email'
                                ? '¡Correo electrónico confirmado! Registro completado.'
                                : '¡Teléfono confirmado! Registro completado.',
                    () => {
                        console.log("[ValidateCode] Usuario presionó OK en alerta de éxito");
                        console.log("[ValidateCode] Contexto:", { isPasswordReset, method });

                        try {
                            if (isPasswordReset) {
                                // Recuperación de contraseña - ir a NewPassword para cambiar contraseña
                                console.log("[ValidateCode] Código verificado para recuperación, navegando a NewPassword");
                                navigation.navigate('NewPassword', { userId });
                            } else {
                                // Verificación de registro - ir directamente a Login
                                console.log("[ValidateCode] Verificación de registro completada, navegando a Login");
                                navigation.reset({
                                    index: 0,
                                    routes: [{ name: 'Login' }],
                                });
                            }
                            console.log("✅ [ValidateCode] Navegación exitosa");
                            // Quitar loading después de navegar
                            setLoading(false);
                        } catch (error) {
                            console.error('❌ [ValidateCode] Error en navegación:', error);
                            showApiErrorAlert('Error de navegación. Intenta de nuevo.');
                            setLoading(false);
                        }
                    }
                );
            } else {
                // IMPORTANTE: Quitar loading en caso de error
                setLoading(false);

                // Manejo específico para verificación telefónica con códigos PVC
                if (method === 'phone' && result.errorCode && result.errorCode.startsWith('PVC-')) {
                    showPVCErrorAlert(
                        result.errorCode,
                        result.message,
                        () => handleResendCode(), // onResendCode
                        () => navigation.navigate('Register'), // onNavigateToRegister
                        () => navigation.navigate('NewPassword', { userId }) // onNavigateToMain
                    );
                } else {
                    showApiErrorAlert(result.message || 'No se pudo verificar el código.');
                }
            }
        } catch (error) {
            console.error('❌ Error en handleVerify:', error);
            showApiErrorAlert('Ocurrió un error inesperado.');
            setLoading(false);
        }
    };

    const handleResendCode = async () => {
        if (loading || !canResend || isNavigating) {
            return; // Prevenir múltiples llamadas
        }

        if (!userId) {
            showApiErrorAlert('No se encontró el usuario. Inicia sesión de nuevo.');
            return;
        }

        setLoading(true);
        try {
            let result;

            // Determinar qué función usar basado en el contexto
            if (isPasswordReset) {
                // Viene de ForgotPassword - usar funciones de recuperación
                console.log('[ValidateCode] Reenviando código de recuperación de contraseña');
                result = method === 'email'
                    ? await resendPasswordResetEmailCode(userId)
                    : await resendPasswordResetCode(userId);
            } else {
                // Viene de registro - usar funciones de verificación
                console.log('[ValidateCode] Reenviando código de verificación de registro');
                result = method === 'email'
                    ? await resendEmailVerification(userId)
                    : await resendPhoneVerification(userId);
            }

            if (result.success) {
                startResendTimer(); // Iniciar timer después de envío exitoso
                showSuccessAlert('✅ Código reenviado',
                    method === 'email'
                        ? 'Revisa tu correo electrónico.'
                        : 'Revisa tus mensajes.');
            } else {
                showApiErrorAlert(result.message || 'No se pudo reenviar el código.');
            }
        } catch (error) {
            console.error('❌ Error en handleResendCode:', error);
            showApiErrorAlert('Ocurrió un error inesperado.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <BaseScreen navColor={globalTheme.gradient[1]}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{ flex: 1 }}
            >
                <View style={{ flex: 1, alignItems: "center", paddingTop: vs(40) }}>
                    <LogoTS size={"sm"}/>

                    <View style={styles.container}>
                        <ScrollView
                            contentContainerStyle={{ flexGrow: 1, alignItems: 'center', paddingBottom: vs(50) }}
                            keyboardShouldPersistTaps="handled"
                        >
                            <Title text="Verificación" />

                            <Text style={styles.description}>
                                Ingresa el código que enviamos a tu {method === 'email' ? 'correo' : 'teléfono'}:
                                {'\n'}
                                <Text style={styles.highlight}>
                                    {method === 'email' ? email : phone}
                                </Text>
                            </Text>

                            <TextInput
                                style={styles.input}
                                placeholder="Código de verificación"
                                placeholderTextColor="#aaa"
                                value={code}
                                onChangeText={setCode}
                                keyboardType="number-pad"
                                maxLength={6}
                            />

                            <TouchableOpacity
                                style={[styles.button, loading && styles.buttonDisabled]}
                                onPress={handleVerify}
                                disabled={loading || isNavigating}
                            >
                                {loading ? (
                                    <ActivityIndicator color="#fff" />
                                ) : (
                                    <Text style={styles.buttonText}>VERIFICAR</Text>
                                )}
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={handleResendCode}
                                disabled={loading || !canResend}
                                style={{
                                    marginTop: vs(12),
                                    flexDirection: 'row',
                                    display: 'flex',
                                    opacity: (!canResend || loading) ? 0.5 : 1
                                }}
                            >
                                {!canResend ? (
                                    <Text style={{ color: globalTheme.text_head, textAlign: 'center' }}>
                                        Reenviar código en {formatTimer(resendTimer)}
                                    </Text>
                                ) : (
                                    <>
                                        <Text style={{ color: globalTheme.text_head}}>¿No recibiste el código? </Text>
                                        <Text style={{color: globalTheme.text_head, textDecorationLine: 'underline'}}>Reenviar</Text>
                                    </>
                                )}
                            </TouchableOpacity>
                        </ScrollView>

                        <TouchableOpacity
                            style={{ bottom: vs(15), position: "absolute", alignSelf: "center" }}
                            onPress={() => navigation.navigate('AvisoPrivacidad', {aceptable: false})}
                        >
                            <Text style={styles.link1}>Aviso de privacidad</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={localStyles.loadingOverlay}>
                    <View style={localStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={localStyles.loadingText}>Verificando código...</Text>
                    </View>
                </BlurView>
            )}
        </BaseScreen>
    );
}

const localStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
};
