import React, { useState, useEffect } from 'react';
import {View, TextInput, TouchableOpacity, ActivityIndicator, KeyboardAvoidingView, Platform, Text, ScrollView} from 'react-native';
import { BlurView } from 'expo-blur';
import BaseScreen from '../../components/BaseScreen';
import Title from '../../components/common/Title';
import { vs } from 'react-native-size-matters';
import { forgotPasswordStyles as styles } from '../../styles/forgotPasswordStyles';
import { showApiErrorAlert, showSuccessAlert, showVerificationErrorAlert, showServerErrorAlert, showValidationAlert, showRetryAlert, showPVCErrorAlert } from '../../utils/alertUtils';
import LogoTS from "../../components/svg/LogoTS";
import {verifyPhoneCode, verifyPhoneNumber} from "../../services/phoneVerificationService";
import {globalTheme} from '../../constants/theme';


export default function ValidateNumberScreen({ navigation, route }: any) {
    const [code, setCode] = useState('');
    const [loading, setLoading] = useState(false);
    const [resendTimer, setResendTimer] = useState(0);
    const [canResend, setCanResend] = useState(true);
    const [isNavigating, setIsNavigating] = useState(false);

    const { userId, phoneNumber } = route.params;

    // Timer para el botón de reenviar código
    useEffect(() => {
        let interval: NodeJS.Timeout | null = null;

        if (resendTimer > 0) {
            interval = setInterval(() => {
                setResendTimer((prev) => {
                    if (prev <= 1) {
                        setCanResend(true);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        // Cleanup function - CRÍTICO para evitar memory leaks
        return () => {
            if (interval) {
                clearInterval(interval);
                interval = null;
            }
        };
    }, [resendTimer]);

    // Cleanup al desmontar el componente
    useEffect(() => {
        return () => {
            // Limpiar cualquier timer activo al desmontar
            setResendTimer(0);
            setCanResend(true);
            setLoading(false);
        };
    }, []);

    // Función para iniciar el timer de reenvío
    const startResendTimer = () => {
        setResendTimer(90); // 1:30 = 90 segundos
        setCanResend(false);
    };

    // Función para formatear el tiempo del timer
    const formatTimer = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const handleVerify = async () => {
        if (isNavigating || loading) {
            return; // Prevenir múltiples llamadas
        }

        if (!code.trim()) {
            showValidationAlert('code', 'Por favor ingresa el código de verificación.');
            return;
        }

        // Validar que el código tenga máximo 6 dígitos
        if (code.trim().length !== 6) {
            showValidationAlert('code', 'El código debe tener exactamente 6 dígitos.');
            return;
        }

        // Validar que solo contenga números
        if (!/^\d{6}$/.test(code.trim())) {
            showValidationAlert('code', 'El código debe contener solo números.');
            return;
        }

        setLoading(true);
        console.log('🔍 Verificando código:', code.trim(), 'para userId:', userId);
        const result = await verifyPhoneCode(userId, code.trim());
        console.log('📥 Resultado de verificación:', result);
        setLoading(false);

        if (result.success) {
            console.log('✅ Verificación exitosa, preparando navegación...');
            setIsNavigating(true);

            showSuccessAlert(
                '✅ Verificado',
                result.alreadyVerified
                    ? 'Tu número ya estaba verificado. ¡Bienvenido!'
                    : '¡Teléfono verificado correctamente!',
                () => {
                    console.log('🎯 Callback de showSuccessAlert ejecutado');
                    console.log('🔍 Estado actual - isNavigating:', isNavigating, 'loading:', loading);

                    // Prevenir navegación múltiple
                    if (!isNavigating) {
                        console.log('❌ Navegación cancelada - isNavigating es false');
                        return;
                    }

                    try {
                        console.log('✅ Verificación exitosa, limpiando navigator y yendo a Login');
                        // Limpiar el stack del navigator y ir directamente a Login
                        navigation.reset({
                            index: 0,
                            routes: [{ name: 'Login' }],
                        });
                        console.log('✅ Navegación ejecutada correctamente');
                    } catch (error) {
                        console.error('❌ Error en navegación:', error);
                    } finally {
                        console.log('🧹 Limpiando estados...');
                        setLoading(false);
                        setIsNavigating(false);
                    }
                }
            );
        } else {
            // Manejo específico de errores PVC del backend
            if (result.errorCode && result.errorCode.startsWith('PVC-')) {
                showPVCErrorAlert(
                    result.errorCode,
                    result.message,
                    () => handleResendCode(), // onResendCode
                    () => navigation.navigate('Register'), // onNavigateToRegister
                    () => navigation.navigate('Login') // onNavigateToMain
                );
            } else if (result.errorCode === 'SERVER_ERROR_500') {
                showServerErrorAlert(500, result.message);
            } else if (result.errorCode === 'VERIFICATION_CODE_EXPIRED') {
                showVerificationErrorAlert('expired', result.message);
            } else if (result.errorCode === 'VERIFICATION_CODE_INVALID') {
                showVerificationErrorAlert('invalid', result.message);
            } else if (result.errorCode === 'NETWORK_ERROR') {
                showRetryAlert('Sin conexión', result.message, () => handleVerify());
            } else if (result.errorCode === 'SERVER_ERROR_TIMEOUT') {
                showRetryAlert('Tiempo agotado', result.message, () => handleVerify());
            } else {
                showApiErrorAlert(result.message || 'No se pudo verificar el código.');
            }
        }
    };

    const handleResendCode = async () => {
        if (loading || !canResend || isNavigating) {
            return; // Prevenir múltiples llamadas
        }

        if (!phoneNumber) {
            showApiErrorAlert('No se encontró el número de teléfono. Vuelve a confirmar tu número.');
            return;
        }

        setLoading(true);
        const result = await verifyPhoneNumber(phoneNumber);
        setLoading(false);

        if (result.success) {
            startResendTimer(); // Iniciar timer después de envío exitoso
            showSuccessAlert('✅ Código reenviado', 'Se ha enviado un nuevo código a tu teléfono.');
        } else {
            // Manejo específico de errores PVC del backend
            if (result.errorCode && result.errorCode.startsWith('PVC-')) {
                showPVCErrorAlert(
                    result.errorCode,
                    result.message,
                    () => handleResendCode(), // onResendCode
                    () => navigation.navigate('Register'), // onNavigateToRegister
                    undefined // onNavigateToMain
                );
            } else if (result.errorCode === 'SERVER_ERROR_500') {
                showServerErrorAlert(500, result.message);
            } else if (result.errorCode === 'NETWORK_ERROR') {
                showRetryAlert('Sin conexión', result.message, () => handleResendCode());
            } else if (result.errorCode === 'SERVER_ERROR_TIMEOUT') {
                showRetryAlert('Tiempo agotado', result.message, () => handleResendCode());
            } else {
                showApiErrorAlert(result.message || 'No se pudo reenviar el código.');
            }
        }
    };

    return (
        <BaseScreen navColor={globalTheme.gradient[1]}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{ flex: 1 }}
            >
                <View style={{ flex: 1, alignItems: "center", paddingTop: vs(40) }}>
                    <LogoTS size={"sm"}/>

                    <View style={styles.container}>
                        <ScrollView
                            contentContainerStyle={{ flexGrow: 1, alignItems: 'center', paddingBottom: vs(50) }}
                            keyboardShouldPersistTaps="handled"
                        >
                            <Title text="Verificar número telefónico" />

                            {phoneNumber && (
                                <Text style={styles.description}>
                                    Ingresa el código de 6 dígitos enviado al número {phoneNumber}
                                </Text>
                            )}

                            <TextInput
                                style={styles.input}
                                keyboardType="number-pad"
                                placeholder="Código de 6 dígitos"
                                placeholderTextColor="#aaa"
                                value={code}
                                maxLength={6}
                                onChangeText={setCode}
                            />

                            <TouchableOpacity
                                style={styles.button}
                                onPress={handleVerify}
                                disabled={loading || isNavigating}
                            >
                                {loading ? (
                                    <ActivityIndicator color="#fff" />
                                ) : (
                                    <Text style={styles.buttonText}>VERIFICAR</Text>
                                )}
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={handleResendCode}
                                disabled={loading || !canResend}
                                style={{
                                    marginTop: vs(12),
                                    opacity: (!canResend || loading) ? 0.5 : 1
                                }}
                            >
                                <Title
                                    text={
                                        !canResend
                                            ? `Reenviar código en ${formatTimer(resendTimer)}`
                                            : "¿No recibiste el código? Reenviar"
                                    }
                                    color="#fff"
                                    size="sm"
                                />
                            </TouchableOpacity>
                        </ScrollView>

                        <TouchableOpacity
                            style={{ bottom: vs(15), position: "absolute", alignSelf: "center" }}
                            onPress={() => navigation.navigate('AvisoPrivacidad', {aceptable: false})}
                        >
                            <Text style={styles.link1}>Aviso de privacidad</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={localStyles.loadingOverlay}>
                    <View style={localStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={localStyles.loadingText}>Verificando teléfono...</Text>
                    </View>
                </BlurView>
            )}
        </BaseScreen>
    );
}

const localStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
};
