import React, {use, useEffect, useState} from 'react';
import {
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    KeyboardAvoidingView,
    Platform,
    View,
    StyleSheet, Switch, Pressable, ActivityIndicator,
} from 'react-native';
import { BlurView } from 'expo-blur';
import {LinearGradient} from 'expo-linear-gradient';
import {Ionicons} from '@expo/vector-icons';
import {loginStyles as styles} from '../../styles/loginStyles';
import {globalTheme} from "../../constants/theme";
import {useValidation, ValidationErrors} from '../../hooks/useValidation';
import {mvs, vs} from 'react-native-size-matters';
import LogoTS from "../../components/svg/LogoTS";
import {useAuth} from '../../context/AuthContext';
import {Alert} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import FingerSwitch from "../../components/login/FingerSwitch";
import EnableBiometricAuthModal from "../../components/login/EnableBiometricAuthModal";
import {showErrorAlert} from "../../utils/alertUtils";
import * as SecureStore from "expo-secure-store";
import censorEmail from "../../utils/censorEmail";
import * as LocalAuthentication from 'expo-local-authentication';
import changeNavigationBarColor from "react-native-navigation-bar-color";
import Header from "../../components/common/Header";


export default function LoginScreen({navigation, route}: any) {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState<ValidationErrors>({});
    const {validateLogin} = useValidation();
    const {login, loginForFinger} = useAuth();

    const [switchIcon, setSwitchIcon] = useState(false);
    const [ebaModal, setEbaModal] = useState<boolean>(false);
    const [biometric, setBiometric] = useState<boolean>(false);
    const [modalErrors, setModalErrors] = useState<ValidationErrors>();
    const [aceptado, setAceptado] = useState<boolean>(route.params?.aceptado || false);

    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    useEffect(() => {
        const loadBiometric = async () => {
            const biometricFlag = await SecureStore.getItemAsync('biometrics');

            if (biometricFlag === "true") {

                const email = await SecureStore.getItemAsync('email');
                const password = await SecureStore.getItemAsync('password');

                setBiometric(true);
                setSwitchIcon(true);
                setEmail(email);
                setPassword(password);
            } else {
                setBiometric(false);
                setSwitchIcon(false);
                setEmail('');
                setPassword('');
            }

        }

        loadBiometric();
    }, []);
    const handleLogin = async () => {
        const validationErrors = validateLogin({email, password});
        setErrors(validationErrors);

        if (Object.keys(validationErrors).length === 0) {
            setLoading(true);
            try {
                const result = await login(email.trim(), password);

                if (!result?.token) {
                    // Usar showErrorAlert en lugar de Alert.alert
                    showErrorAlert({
                        error: {
                            code: result?.code || 'AUTH_LOGIN_FAILED',
                            message: result?.msg || 'Error al iniciar sesión'
                        }
                    });
                }
            } catch (error) {
                console.error('❌ Error en handleLogin:', error);
                showErrorAlert({
                    error: {
                        code: 'AUTH_LOGIN_FAILED',
                        message: 'Error inesperado al iniciar sesión'
                    }
                });
            } finally {
                setLoading(false);
            }
        }
    };

    const handleEnableAuth = async (emailModal: string, passwordModal: string) => {
        const validationErrors = validateLogin({email: emailModal, password: passwordModal});

        setModalErrors(validationErrors);

        if (Object.keys(validationErrors).length === 0) {
            const result = await loginForFinger(emailModal.trim(), passwordModal);

            if (result.error){
                handleCloseModal(false)
                showErrorAlert({error: {message: result.msg}})
            } else {
                await rememberUser();
                handleCloseModal(true);
            }


        } else {
            showErrorAlert({error: { message: 'Credenciales incorrectas'}});
        }

    }

    const rememberUser =  async () => {
        setEmail(await SecureStore.getItemAsync("email"));
        setPassword(await SecureStore.getItemAsync("password"));
        setBiometric(true);
    }

    const handleOpenModal = () => {
        setSwitchIcon(!switchIcon);
        if (!switchIcon) {
            setEbaModal(true);
        } else {
            deleteStoredAccount()
        }
    }

    const handleCloseModal = (result: boolean) => {
        setEbaModal(false);
        setSwitchIcon(result);
    }

    const deleteStoredAccount = async () => {

        setBiometric(false);
        setSwitchIcon(false);

        await SecureStore.setItemAsync('email', '');
        await SecureStore.setItemAsync('password', '');
        await SecureStore.setItemAsync('biometrics', 'false')

        setEmail('');
        setPassword('');

        setErrors({});
    }

    const onSignInPress = async () => {
        setLoading(true);
        try {
            const hasHardware = await LocalAuthentication.hasHardwareAsync();
            const isEnrolled = await LocalAuthentication.isEnrolledAsync();

            if (!hasHardware || !isEnrolled) {
                showErrorAlert({ error: { message: 'Biometría no disponible en este dispositivo' }});
                return;
            }

            const result = await LocalAuthentication.authenticateAsync({
                promptMessage: 'Usa tu huella para ingresar',
                fallbackLabel: 'Usar contraseña',
            });

            if (result.success) {
                const loginResult = await login(email.trim(), password);

                if (!loginResult?.token) {
                    showErrorAlert({
                        error: {
                            code: loginResult?.code || 'AUTH_BIOMETRIC_LOGIN_FAILED',
                            message: loginResult?.msg || 'Error al iniciar sesión con biometría'
                        }
                    });
                }
            } else {
                showErrorAlert({ error: { message: 'Autenticación fallida' }});
            }
        } catch (err) {
            console.error(err);
            showErrorAlert({ error: { message: 'Error al autenticar' }});
        } finally {
            setLoading(false);
        }
    };


    return (

        <SafeAreaView style={{flex: 1, backgroundColor: globalTheme.gradient[1]}} edges={['top']} >

            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                    style={StyleSheet.absoluteFillObject}
                />
            </View>


            <KeyboardAvoidingView
                style={{flex: 1}}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}

            >
                <View style={styles.wrapper}>
                    <ScrollView
                        contentContainerStyle={styles.scrollContainer}
                        keyboardShouldPersistTaps="handled"
                    >
                        <LogoTS size="sm"/>

                        <View style={styles.form}>
                            {biometric ? (
                                <>
                                    <Text style={styles.title}>Ingresa con tu huella al correo:  </Text>
                                    <Text style={styles.label}>{censorEmail(email)}</Text>

                                </>
                            ) : (
                                <>
                                    <Text style={styles.title}>INICIO DE SESIÓN</Text>

                                    <Text style={styles.label}>USUARIO</Text>
                                    <View style={[styles.inputContainer, errors.email ? {marginBottom: vs(0)} : {marginBottom: 20}]}>
                                        <TextInput
                                            style={styles.input}
                                            value={email}
                                            onChangeText={(text) => {
                                                setEmail(text);
                                                if (errors.email) setErrors({...errors, email: undefined});
                                            }}
                                            placeholder="Correo electrónico"
                                            placeholderTextColor={globalTheme.text_placeholder}
                                            autoCapitalize="none"
                                        />
                                    </View>
                                    {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}

                                    <Text style={styles.label}>CONTRASEÑA</Text>
                                    <View style={[styles.inputContainer, errors.password ? {marginBottom: vs(0)} : {marginBottom: 20}]}>
                                        <TextInput
                                            style={[styles.input]}
                                            value={password}
                                            onChangeText={(text) => {
                                                setPassword(text);
                                                if (errors.password) setErrors({...errors, password: undefined});
                                            }}
                                            placeholder="Contraseña"
                                            placeholderTextColor={globalTheme.text_placeholder}
                                            secureTextEntry={!showPassword}
                                        />
                                        <TouchableOpacity
                                            style={styles.iconOverlay}
                                            onPress={() => setShowPassword(!showPassword)}
                                        >
                                            <Ionicons
                                                name={!showPassword ? 'eye-off-outline' : 'eye-outline'}
                                                size={vs(15)}
                                                color={globalTheme.container_dark}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                    {errors.password && (
                                        <Text style={styles.errorText}>{errors.password}</Text>
                                    )}

                                </>
                            )}

                            <View style={{flexDirection: "row", width: '100%', justifyContent: "space-between", alignItems: "center", marginBottom: vs(20)}}>
                                <FingerSwitch isEnabled={switchIcon} handleOpenModal={handleOpenModal}/>
                                {biometric && (
                                    <Pressable
                                        onPress={() => deleteStoredAccount()}
                                    >
                                        <Text style={styles.link}>No es mi cuenta</Text>
                                    </Pressable>
                                )}
                            </View>

                            <EnableBiometricAuthModal
                                ebaModal={ebaModal}
                                handleCloseModal={handleCloseModal}
                                handleEnableAuth={handleEnableAuth}
                                modalErrors={modalErrors}
                            />


                            <TouchableOpacity
                                style={[
                                    styles.loginBtn,
                                    biometric && {marginTop: vs(100), marginBottom: vs(60)},
                                    loading && {opacity: 0.6}
                                ]}
                                onPress={() => (biometric ? onSignInPress() : handleLogin())}
                                disabled={loading}
                            >

                                <Text style={styles.loginText}>
                                    {loading
                                        ? (biometric ? 'AUTENTICANDO...' : 'INGRESANDO...')
                                        : (biometric ? 'CONTINUAR' : 'INGRESAR')
                                    }
                                </Text>
                            </TouchableOpacity>

                            {/*<GoogleButton/>*/}

                            <TouchableOpacity onPress={()=> navigation.navigate('ForgotPassword')}>
                                <Text style={styles.link}>Olvidé mi contraseña</Text>
                            </TouchableOpacity>

                            <View style={{
                                flexDirection: 'row',
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginTop: vs(10),
                            }}>
                                <Text style={styles.registerText}>¿No tienes cuenta? </Text>
                                <TouchableOpacity onPress={() => navigation.navigate('Register', {aceptado: false})}>
                                    <Text style={styles.registerLink}>Regístrate</Text>
                                </TouchableOpacity>
                            </View>

                            {/* Aviso de Privacidad */}
                            <View style={localStyles.avisoButton}>
                                <TouchableOpacity
                                    style={localStyles.aviso}
                                    onPress={() => navigation.navigate('AvisoPrivacidad', {
                                        aceptable: false,
                                        previousScreen: 'login',
                                        aceptado: aceptado,
                                        form: { email, password }
                                    })}
                                >
                                    <Text style={localStyles.link}>AVISO DE PRIVACIDAD</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </ScrollView>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={loadingOverlayStyles.loadingOverlay}>
                    <View style={loadingOverlayStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={loadingOverlayStyles.loadingText}>
                            {biometric ? 'Autenticando...' : 'Iniciando sesión...'}
                        </Text>
                    </View>
                </BlurView>
            )}
        </SafeAreaView>
    );
}

const localStyles = StyleSheet.create({
    avisoButton: {
        alignItems: 'center',
        gap: vs(5),
        marginTop: vs(10),
        width: '100%',
    },
    link: {
        color: globalTheme.text_light,
        fontSize: vs(10),
        textDecorationLine: 'underline',
        textAlign: 'center',
    },
    aviso: {
        alignItems: 'center',
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'center',
    }
});

const loadingOverlayStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
};
