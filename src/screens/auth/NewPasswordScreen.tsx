// src/screens/NewPasswordScreen.tsx

import React, {useEffect, useState} from 'react';
import {
    View,
    TextInput,
    TouchableOpacity,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    ScrollView,
    Text,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {globalTheme} from '../../constants/theme';
import Title from '../../components/common/Title';
import {Ionicons} from '@expo/vector-icons';
import { resetPassword } from '../../services/emailVerificationService';
import { showApiErrorAlert, showSuccessAlert } from '../../utils/alertUtils';
import { newPasswordStyles as styles } from '../../styles/newPasswordStyles';
import LogoTS from "../../components/svg/LogoTS";
import {vs} from "react-native-size-matters";
import {SafeAreaView} from 'react-native-safe-area-context';
import changeNavigationBarColor from "react-native-navigation-bar-color";

export default function NewPasswordScreen({ route, navigation }: any) {
    const { userId } = route.params;
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [loading, setLoading] = useState(false);
    const [focusedInput, setFocusedInput] = useState<string | null>(null);

    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    const handleSave = async () => {
        if (newPassword.length < 6) {
            showApiErrorAlert('La nueva contraseña debe tener al menos 6 caracteres.');
            return;
        }

        if (newPassword !== confirmPassword) {
            showApiErrorAlert('Las contraseñas no coinciden.');
            return;
        }

        try {
            console.log("[NewPassword] Iniciando cambio de contraseña, activando loading");
            setLoading(true);

            const result = await resetPassword(userId, newPassword);
            console.log("[NewPassword] Respuesta recibida:", result);

            if (result.success) {
                console.log("[NewPassword] Cambio exitoso, mostrando alerta (loading sigue activo)");
                showSuccessAlert(
                    '✅ Contraseña actualizada',
                    'Ahora puedes iniciar sesión con tu nueva contraseña.',
                    () => {
                        // Navegar cuando el usuario presione OK
                        console.log("[NewPassword] Usuario presionó OK, navegando a Login");

                        // Limpiar completamente el stack y navegar a Login
                        setTimeout(() => {
                            navigation.reset({
                                index: 0,
                                routes: [{ name: 'Login' }],
                            });
                            // Quitar loading después de navegar
                            console.log("[NewPassword] Navegación completada, quitando loading");
                            setLoading(false);
                        }, 100); // Pequeño delay para evitar conflictos de transición
                    }
                );
            } else {
                console.log("[NewPassword] Error en cambio, quitando loading");
                setLoading(false);
                showApiErrorAlert(result.message || 'No se pudo cambiar la contraseña.');
            }
        } catch (error) {
            console.error('❌ Error en handleSave:', error);
            setLoading(false);
            showApiErrorAlert('Ocurrió un error inesperado.');
        }
    };

    return (

            <SafeAreaView style={{ flex: 1, backgroundColor: globalTheme.gradient[1] }} edges={['top']}>
                <View style={StyleSheet.absoluteFill}>
                    <LinearGradient colors={[globalTheme.gradient[0], globalTheme.gradient[1]]} style={StyleSheet.absoluteFillObject} />
                </View>
                
                <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined} style={{flex: 1}}>
                    <View style={styles.wrapper}>
                        <ScrollView
                            contentContainerStyle={styles.scrollContainer}
                            keyboardShouldPersistTaps="handled"
                        >
                            <LogoTS size={"sm"} style={{alignSelf: 'center', marginBottom: vs(30)}}/>

                            <View style={styles.form}>
                                <View style={styles.titleContainer}>
                                    <Text style={styles.title}>NUEVA CONTRASEÑA</Text>
                                    <Text style={styles.subtitle}>
                                        Ingresa tu nueva contraseña para completar la recuperación
                                    </Text>
                                </View>

                                <View style={styles.inputContainer}>
                                    <TextInput
                                        style={[
                                            styles.input,
                                            focusedInput === 'newPassword' && styles.inputFocused
                                        ]}
                                        placeholder="Nueva contraseña"
                                        placeholderTextColor={globalTheme.text_placeholder}
                                        secureTextEntry={!showPassword}
                                        value={newPassword}
                                        onChangeText={setNewPassword}
                                        onFocus={() => setFocusedInput('newPassword')}
                                        onBlur={() => setFocusedInput(null)}
                                    />
                                    <TouchableOpacity
                                        onPress={() => setShowPassword(!showPassword)}
                                        style={styles.iconOverlay}
                                        activeOpacity={0.7}
                                    >
                                        <Ionicons
                                            name={!showPassword ? 'eye-off-outline' : 'eye-outline'}
                                            size={vs(18)}
                                            color={globalTheme.gradient[0]}
                                        />
                                    </TouchableOpacity>
                                </View>

                                <View style={styles.inputContainer}>
                                    <TextInput
                                        style={[
                                            styles.input,
                                            focusedInput === 'confirmPassword' && styles.inputFocused
                                        ]}
                                        placeholder="Confirmar contraseña"
                                        placeholderTextColor={globalTheme.text_placeholder}
                                        secureTextEntry={!showConfirmPassword}
                                        value={confirmPassword}
                                        onChangeText={setConfirmPassword}
                                        onFocus={() => setFocusedInput('confirmPassword')}
                                        onBlur={() => setFocusedInput(null)}
                                    />
                                    <TouchableOpacity
                                        onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                                        style={styles.iconOverlay}
                                        activeOpacity={0.7}
                                    >
                                        <Ionicons
                                            name={!showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
                                            size={vs(18)}
                                            color={globalTheme.gradient[0]}
                                        />
                                    </TouchableOpacity>
                                </View>

                                <TouchableOpacity
                                    style={[styles.loginBtn, loading && { opacity: 0.7 }]}
                                    onPress={handleSave}
                                    disabled={loading}
                                    activeOpacity={0.8}
                                >
                                    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                        {loading && (
                                            <ActivityIndicator
                                                size="small"
                                                color={globalTheme.text_head}
                                                style={{ marginRight: vs(8) }}
                                            />
                                        )}
                                        <Text style={styles.buttonText}>
                                            {loading ? 'Guardando contraseña...' : 'Guardar contraseña'}
                                        </Text>
                                    </View>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.privacyLink}
                                    onPress={() => navigation.navigate('AvisoPrivacidad', {aceptable: false})}
                                >
                                    <Text style={styles.link1}>Aviso de privacidad</Text>
                                </TouchableOpacity>
                            </View>
                        </ScrollView>
                    </View>
                </KeyboardAvoidingView>

                {/* Loading Overlay - Mismo estilo que ForgotPassword */}
                {loading && (
                    <BlurView intensity={20} tint="dark" style={localStyles.loadingOverlay}>
                        <View style={localStyles.loadingCard}>
                            <ActivityIndicator size="large" color={globalTheme.gradient[0]} />
                            <Text style={localStyles.loadingText}>Guardando contraseña...</Text>
                        </View>
                    </BlurView>
                )}
            </SafeAreaView>

    );
}

// Estilos locales para el loading (copiados de ForgotPasswordScreen)
const localStyles = StyleSheet.create({
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: vs(25),
        borderRadius: 15,
        alignItems: 'center',
        minWidth: vs(140),
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: vs(12),
        color: '#000000',
        fontSize: vs(13), // Fuente más pequeña como solicitaste
        fontWeight: '600',
        textAlign: 'center',
    },
});
