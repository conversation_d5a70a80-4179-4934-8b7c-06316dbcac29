import React, { useState } from 'react';
import {View, TextInput, TouchableOpacity, ActivityIndicator, KeyboardAvoidingView, Platform, Text, ScrollView} from 'react-native';
import { BlurView } from 'expo-blur';
import BaseScreen from '../../components/BaseScreen';
import Title from '../../components/common/Title';
import { vs } from 'react-native-size-matters';
import { forgotPasswordStyles as styles } from '../../styles/forgotPasswordStyles';
import { showApiErrorAlert, showSuccessAlert, showValidationAlert, showPVCErrorAlert } from '../../utils/alertUtils';
import LogoTS from "../../components/svg/LogoTS";
import { verifyPhoneNumber } from "../../services/phoneVerificationService";
import {globalTheme} from '../../constants/theme';

/**
 * ConfirmPhoneScreen - Pantalla para confirmar número de teléfono
 *
 * NOTA: Esta pantalla ya NO se usa en el flujo normal de registro.
 * El registro ahora va directamente a ValidateNumber porque el backend
 * ya envía SMS automáticamente durante el registro.
 *
 * Esta pantalla se mantiene para casos especiales donde se necesite
 * confirmar un número antes de enviar SMS (ej: recuperación de contraseña
 * por teléfono si se implementa en el futuro).
 */
export default function ConfirmPhoneScreen({ navigation, route }: any) {
    const [phone, setPhone] = useState('');
    const [loading, setLoading] = useState(false);

    // Obtener parámetros de la navegación
    const { registeredPhone, isPasswordReset } = route.params || {};

    React.useEffect(() => {
        if (registeredPhone) {
            setPhone(registeredPhone);
        }
    }, [registeredPhone]);

    const handleConfirmPhone = async () => {
        if (!phone.trim()) {
            showValidationAlert('phone', 'Por favor ingresa tu número de teléfono.');
            return;
        }

        // Validar formato de teléfono (10 dígitos)
        if (!/^\d{10}$/.test(phone.trim())) {
            showValidationAlert('phone', 'El teléfono debe tener exactamente 10 dígitos.');
            return;
        }

        setLoading(true);
        const result = await verifyPhoneNumber(phone.trim());
        setLoading(false);

        if (result.success) {
            showSuccessAlert(
                '✅ SMS Enviado',
                `Se ha enviado un código de verificación al número ${phone}.`,
                () => {
                    // Mantener loading durante la navegación
                    navigation.navigate('ValidateNumber', {
                        userId: result.userId,
                        phoneNumber: phone.trim()
                    });
                    setLoading(false); // Se quita cuando ya navegó
                }
            );
        } else {
            // Manejo específico de errores PVC del backend
            if (result.errorCode && result.errorCode.startsWith('PVC-')) {
                showPVCErrorAlert(
                    result.errorCode,
                    result.message,
                    undefined, // onResendCode - no aplica aquí
                    () => navigation.navigate('Register'), // onNavigateToRegister
                    undefined // onNavigateToMain - no aplica aquí
                );
            } else {
                showApiErrorAlert(result.message || 'No se pudo enviar el código de verificación.');
            }
        }
    };

    return (
        <BaseScreen navColor={globalTheme.gradient[1]}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                style={{ flex: 1 }}
            >
                <View style={{ flex: 1, alignItems: "center", paddingTop: vs(40) }}>
                    <LogoTS size={"sm"}/>

                    <View style={styles.container}>
                        <ScrollView
                            contentContainerStyle={{ flexGrow: 1, alignItems: 'center', paddingBottom: vs(50) }}
                            keyboardShouldPersistTaps="handled"
                        >
                            <Title text={isPasswordReset ? "Recuperar contraseña" : "Confirmar número de teléfono"} />

                            <Text style={styles.description}>
                                {isPasswordReset
                                    ? "Ingresa tu número de teléfono para recibir un código de recuperación por SMS."
                                    : "Confirma tu número de teléfono para recibir el código de verificación por SMS."
                                }
                            </Text>

                            <TextInput
                                style={styles.input}
                                keyboardType="phone-pad"
                                placeholder="Número de teléfono (10 dígitos)"
                                placeholderTextColor="#aaa"
                                value={phone}
                                maxLength={10}
                                onChangeText={setPhone}
                            />

                            <TouchableOpacity
                                style={styles.button}
                                onPress={handleConfirmPhone}
                                disabled={loading}
                            >
                                {loading ? (
                                    <ActivityIndicator color="#fff" />
                                ) : (
                                    <Text style={styles.buttonText}>ENVIAR CÓDIGO</Text>
                                )}
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={() => navigation.navigate('Register')}
                                disabled={loading}
                                style={{ marginTop: vs(12) }}
                            >
                                <Title text="¿Número incorrecto? Volver al registro" color="#fff" size="sm" />
                            </TouchableOpacity>
                        </ScrollView>

                        <TouchableOpacity
                            style={{ bottom: vs(15), position: "absolute", alignSelf: "center" }}
                            onPress={() => navigation.navigate('AvisoPrivacidad', {aceptable: false})}
                        >
                            <Text style={styles.link1}>Aviso de privacidad</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={localStyles.loadingOverlay}>
                    <View style={localStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={localStyles.loadingText}>Enviando SMS...</Text>
                    </View>
                </BlurView>
            )}
        </BaseScreen>
    );
}

const localStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
};
