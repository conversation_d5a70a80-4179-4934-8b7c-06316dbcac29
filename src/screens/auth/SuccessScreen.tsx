import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import BaseScreen from '../../components/BaseScreen';
import { successStyles as styles } from '../../styles/successStyles';
import * as Animatable from 'react-native-animatable';
import LogoTS from "../../components/svg/LogoTS";
import {globalTheme} from "../../constants/theme";


export default function SuccessScreen({ navigation }: any) {
    return (
        <BaseScreen scroll={false} navColor={globalTheme.gradient[1]}>
            <Animatable.View
                animation="bounceIn"
                duration={1500}
                style={styles.container}
            >
                <LogoTS style={"md"} />
                <Text style={styles.title}>¡Registro exitoso!</Text>
                <Text style={styles.description}>
                    Tu cuenta ha sido creada correctamente. Ahora puedes iniciar sesión.
                </Text>

                <TouchableOpacity
                    style={styles.button}
                    onPress={() => navigation.navigate('Login')}
                >
                    <Text style={styles.buttonText}>IR A INICIAR SESIÓN</Text>
                </TouchableOpacity>
            </Animatable.View>
        </BaseScreen>
    );
}
