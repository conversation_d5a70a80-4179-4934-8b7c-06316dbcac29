import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    ActivityIndicator
} from 'react-native';
import { BlurView } from 'expo-blur';
import LogoTS from "../../components/svg/LogoTS";
import { useAuth } from '../../context/AuthContext';
import {showErrorAlert, showApiErrorAlert, showSuccessAlert, showNoChangesAlert, showValidationAlert, showServerErrorAlert, showRegistrationErrorAlert, showFieldValidationErrors} from '../../utils/alertUtils';
import { vs } from "react-native-size-matters";
import {SafeAreaView} from "react-native-safe-area-context";
import {LinearGradient} from "expo-linear-gradient";
import {globalTheme} from '../../constants/theme';
import { verifyPhoneNumber } from '../../services/phoneVerificationService';
import {RouteProp, useRoute} from "@react-navigation/native";
import {RootStackParamList} from "../../navigation/AppNavigator";
import {Ionicons} from "@expo/vector-icons";
import { BackHandler } from 'react-native';
import changeNavigationBarColor from "react-native-navigation-bar-color";

export default function RegisterScreen({ navigation }: any) {
    const route = useRoute<RouteProp<RootStackParamList, 'Register'>>();

    useEffect(() => {
        changeNavigationBarColor(globalTheme.gradient[1], true);
    }, []);

    const [form, setForm] = useState({
        company: 'GRUPO TORRES',
        workerId: route.params?.form?.workerId || '',
        curp: route.params?.form?.curp || '',
        phone: route.params?.form?.phone || '',
        email: route.params?.form?.email || '',
        password: route.params?.form?.password || '',
        confirmPassword: route.params?.form?.confirmPassword || '',
    });

    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [loading, setLoading] = useState(false);
    const { register } = useAuth();

    // Referencia al timeout para poder limpiarlo
    const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);


    const [aceptado, setAceptado] = useState<boolean>(route.params?.aceptado || false);

    // Cleanup del loading y timeout al desmontar el componente
    useEffect(() => {
        return () => {
            setLoading(false);
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                timeoutRef.current = null;
            }
        };
    }, []);

    const handleChange = (key: string, value: any) => {
        setForm({ ...form, [key]: value });
        if (errors[key]) setErrors({ ...errors, [key]: undefined });
    };

    // Validación específica para CURP en tiempo real
    const validateCURPRealTime = (curp: string) => {
        if (!curp) {
            return 'El CURP es obligatorio.';
        }

        if (typeof curp !== 'string') {
            return 'El CURP debe ser una cadena de texto.';
        }

        if (curp.length !== 18) {
            return 'La CURP debe tener exactamente 18 caracteres';
        }

        // Validación de formato CURP
        const curpRegex = /^[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9A-Z][0-9]$/;
        if (!curpRegex.test(curp.toUpperCase())) {
            return 'El formato del CURP no es válido.';
        }

        return null; // Sin errores
    };

    const handleCURPChange = (value: string) => {
        // Filtrar solo números y letras, sin símbolos
        const filteredValue = value.replace(/[^A-Za-z0-9]/g, '');
        const upperValue = filteredValue.toUpperCase();
        setForm({ ...form, curp: upperValue });

        // Validar solo si tiene contenido
        if (upperValue.trim()) {
            const error = validateCURPRealTime(upperValue);
            if (error) {
                setErrors({ ...errors, curp: error });
            } else {
                setErrors({ ...errors, curp: undefined });
            }
        } else {
            setErrors({ ...errors, curp: undefined });
        }
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        // Validación número de empleado (siguiendo las validaciones del backend)
        if (!form.workerId.trim()) {
            newErrors.workerId = 'El número de empleado es obligatorio.';
        } else if (typeof form.workerId !== 'string') {
            newErrors.workerId = 'El número de empleado debe ser una cadena de texto.';
        } else if (form.workerId.trim().length < 3) {
            newErrors.workerId = 'Debe tener al menos 3 caracteres';
        }

        // Validación CURP 
        if (!form.curp.trim()) {
            newErrors.curp = 'El CURP es obligatorio.';
        } else if (typeof form.curp !== 'string') {
            newErrors.curp = 'El CURP debe ser una cadena de texto.';
        } else if (form.curp.trim().length !== 18) {
            newErrors.curp = 'La CURP debe tener exactamente 18 caracteres';
        } else if (!/^[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9A-Z][0-9]$/.test(form.curp.trim().toUpperCase())) {
            newErrors.curp = 'El formato del CURP no es válido.';
        }

        // Validación teléfono
        if (!form.phone.trim()) {
            newErrors.phone = 'El número de teléfono es obligatorio.';
        } else if (typeof form.phone !== 'string') {
            newErrors.phone = 'El número de teléfono debe ser una cadena de texto.';
        } else if (!/^[0-9]{10}$/.test(form.phone.trim())) {
            newErrors.phone = 'Debe tener exactamente 10 dígitos';
        }

        // Validación email 
        if (!form.email.trim()) {
            newErrors.email = 'El correo electrónico es obligatorio.';
        } else if (typeof form.email !== 'string') {
            newErrors.email = 'El correo electrónico debe ser una cadena de texto.';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email.trim())) {
            newErrors.email = 'El correo electrónico no es válido.';
        }

        // Validación contraseña
        if (!form.password.trim()) {
            newErrors.password = 'La contraseña es obligatoria.';
        } else if (typeof form.password !== 'string') {
            newErrors.password = 'La contraseña debe ser una cadena de texto.';
        } else if (form.password.length < 6) {
            newErrors.password = 'La contraseña debe tener al menos 6 caracteres.';
        } else if (form.password.length > 50) {
            newErrors.password = 'La contraseña no puede tener más de 50 caracteres.';
        }

        // Validación confirmación de contraseña
        if (!form.confirmPassword.trim()) {
            newErrors.confirmPassword = 'La confirmación de contraseña es obligatoria.';
        } else if (form.password !== form.confirmPassword) {
            newErrors.confirmPassword = 'Las contraseñas no coinciden';
        }

        return newErrors;
    };

    const handleNext = async () => {
        const validationErrors = validateForm();
        setErrors(validationErrors);

        if (!aceptado) {
            showNoChangesAlert({
                message: 'Acepta el aviso de privacidad',
                description: 'Por favor lee y acepta el aviso de privacidad.'
            });
            return;
        }

        if (Object.keys(validationErrors).length === 0) {
            setLoading(true);

            // Timeout más corto para CURP no existente (8 segundos)
            timeoutRef.current = setTimeout(() => {
                setLoading(false);
                timeoutRef.current = null;
                showServerErrorAlert(408, 'La solicitud está tardando demasiado. Esto puede indicar que el CURP no existe en el sistema o hay problemas de conexión. Verifica los datos e intenta de nuevo.');
                console.error('⏰ Tiempo de espera agotado en el registro - posible CURP no existente.');
            }, 8000);

            try {
                const payload = {
                    curp: form.curp.trim().toUpperCase(),
                    email: form.email.trim().toLowerCase(),
                    password: form.password,
                    phone_number: form.phone.trim(),
                    employee_number: form.workerId.trim(),
                };

                console.log('🚀 Iniciando registro con payload:', { ...payload, password: '[HIDDEN]' });
                const response = await register(payload);

                // Limpiar timeout si la respuesta llega a tiempo
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                    timeoutRef.current = null;
                }

                console.log('✅ Respuesta del registro:', response);

                if (response?.error) {
                    setLoading(false); // Asegurar que se quite el loading

                    // Manejo específico de errores de registro
                    if (response.errorCode) {
                        // Mensaje específico para CURP no encontrado
                        if (response.errorCode === 'RVS-001' || response.message?.toLowerCase().includes('curp')) {
                            showRegistrationErrorAlert(
                                response.errorCode,
                                'El CURP ingresado no se encuentra registrado en el sistema. Verifica que sea correcto o contacta a tu administrador.',
                                response.validationErrors,
                                () => navigation.navigate('Login'), // onNavigateToLogin
                                () => handleNext() // onRetry
                            );
                        } else {
                            showRegistrationErrorAlert(
                                response.errorCode,
                                response.message,
                                response.validationErrors,
                                () => navigation.navigate('Login'), // onNavigateToLogin
                                () => handleNext() // onRetry
                            );
                        }
                    } else {
                        // Fallback para errores legacy
                        showErrorAlert(response);
                    }
                } else {
                    // Registro exitoso, ahora enviar SMS de verificación
                    console.log('✅ Registro exitoso, enviando SMS de verificación...');

                    try {
                        const smsResult = await verifyPhoneNumber(form.phone.trim());

                        if (smsResult.success) {
                            console.log('✅ SMS enviado exitosamente después del registro');
                            showSuccessAlert(
                                '✅ Registro exitoso',
                                `Se ha enviado un código de verificación al número ${form.phone.trim()}. Ingresa el código para completar tu registro.`,
                                () => {
                                    navigation.navigate('ValidateNumber', {
                                        userId: response.user_id,
                                        phoneNumber: form.phone.trim()
                                    });
                                    // Quitar loading después de navegar
                                    setLoading(false);
                                }
                            );
                        } else {
                            console.error('❌ Error al enviar SMS después del registro:', smsResult.message);
                            // Si falla el SMS, aún así permitir continuar pero informar al usuario
                            showSuccessAlert(
                                '✅ Registro exitoso',
                                `Registro completado. Hubo un problema enviando el SMS. Puedes usar "Reenviar código" en la siguiente pantalla.`,
                                () => {
                                    navigation.navigate('ValidateNumber', {
                                        userId: response.user_id,
                                        phoneNumber: form.phone.trim()
                                    });
                                    // Quitar loading después de navegar
                                    setLoading(false);
                                }
                            );
                        }
                    } catch (smsError: any) {
                        console.error('❌ Excepción al enviar SMS después del registro:', smsError);
                        // Si hay excepción, aún así permitir continuar
                        showSuccessAlert(
                            '✅ Registro exitoso',
                            `Registro completado. Hubo un problema enviando el SMS. Puedes usar "Reenviar código" en la siguiente pantalla.`,
                            () => {
                                navigation.navigate('ValidateNumber', {
                                    userId: response.user_id,
                                    phoneNumber: form.phone.trim()
                                });
                                // Quitar loading después de navegar
                                setLoading(false);
                            }
                        );
                    }
                }
            } catch (error: any) {
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                    timeoutRef.current = null;
                }
                setLoading(false); // Asegurar que se quite el loading

                console.error('❌ Excepción capturada en registro:', error);

                // Manejo específico para diferentes tipos de errores
                if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
                    showServerErrorAlert(0, 'Error de conexión. Verifica tu internet e intenta de nuevo.');
                } else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
                    showServerErrorAlert(408, 'La solicitud tardó demasiado. Esto puede indicar que el CURP no existe en el sistema.');
                } else {
                    showServerErrorAlert(500, 'Error inesperado durante el registro. Verifica los datos e intenta de nuevo.');
                }
            }
        } else {
            // Mostrar alerta específica para el primer error encontrado
            const firstErrorField = Object.keys(validationErrors)[0];
            const firstErrorMessage = validationErrors[firstErrorField];
            showValidationAlert(firstErrorField, firstErrorMessage);
        }
    };

    useEffect(() => {
        const backAction = () => {
            navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
            });
            return true; // Prevenir el comportamiento predeterminado de retroceso
        };

        // Agregar el listener de retroceso
        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

        return () => backHandler.remove();
    }, [navigation]);

    return (
        <SafeAreaView style={{flex: 1}} edges={['top']}>
            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    colors={[globalTheme.gradient[0], globalTheme.gradient[1]]}
                    style={StyleSheet.absoluteFillObject}
                />
            </View>

            <KeyboardAvoidingView
                style={{flex: 1}}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <View style={localStyles.wrapper}>
                    <ScrollView
                        contentContainerStyle={localStyles.scrollContainer}
                        keyboardShouldPersistTaps="handled"
                    >
                        <LogoTS size="sm"/>

                        <View style={localStyles.formContainer}>
                            <Text style={localStyles.title}>REGISTRO</Text>

                            <View style={localStyles.inputsContainer}>
                                {/* Número de trabajador */}
                                <Text style={localStyles.label}>N° DE TRABAJADOR</Text>
                                <TextInput
                                    style={localStyles.input}
                                    placeholder="0218889"
                                    keyboardType="phone-pad"
                                    value={form.workerId}
                                    onChangeText={(val) => handleChange('workerId', val)}
                                />
                                {errors.workerId && <Text style={localStyles.errorText}>{errors.workerId}</Text>}

                                {/* CURP */}
                                <Text style={localStyles.label}>CURP</Text>
                                <TextInput
                                    style={localStyles.input}
                                    placeholder="CURP (18 caracteres)"
                                    value={form.curp}
                                    autoCapitalize="characters"
                                    maxLength={18}
                                    onChangeText={handleCURPChange}
                                />
                                {errors.curp && <Text style={localStyles.errorText}>{errors.curp}</Text>}

                                {/* Teléfono */}
                                <Text style={localStyles.label}>TELÉFONO</Text>
                                <TextInput
                                    style={localStyles.input}
                                    placeholder="5512345678"
                                    keyboardType="phone-pad"
                                    value={form.phone}
                                    onChangeText={(val) => handleChange('phone', val)}
                                />
                                {errors.phone && <Text style={localStyles.errorText}>{errors.phone}</Text>}

                                {/* Correo */}
                                <Text style={localStyles.label}>CORREO</Text>
                                <TextInput
                                    style={localStyles.input}
                                    placeholder="<EMAIL>"
                                    keyboardType="email-address"
                                    autoCapitalize="none"
                                    value={form.email}
                                    onChangeText={(val) => handleChange('email', val)}
                                />
                                {errors.email && <Text style={localStyles.errorText}>{errors.email}</Text>}

                                {/* Contraseña */}
                                <Text style={localStyles.label}>CONTRASEÑA</Text>
                                <View style={localStyles.passwordContainer}>
                                    <TextInput
                                        style={localStyles.passwordInput}
                                        placeholder="********"
                                        secureTextEntry={!showPassword}
                                        value={form.password}
                                        onChangeText={(val) => handleChange('password', val)}
                                    />
                                    <TouchableOpacity 
                                        style={localStyles.eyeIcon}
                                        onPress={() => setShowPassword(!showPassword)}
                                    >
                                        <Ionicons 
                                            name={showPassword ? 'eye-off' : 'eye'} 
                                            size={vs(20)} 
                                            color="#666" 
                                        />
                                    </TouchableOpacity>
                                </View>
                                {errors.password && <Text style={localStyles.errorText}>{errors.password}</Text>}

                                {/* Confirmar Contraseña */}
                                <Text style={localStyles.label}>CONFIRMAR CONTRASEÑA</Text>
                                <View style={localStyles.passwordContainer}>
                                    <TextInput
                                        style={localStyles.passwordInput}
                                        placeholder="********"
                                        secureTextEntry={!showConfirmPassword}
                                        value={form.confirmPassword}
                                        onChangeText={(val) => handleChange('confirmPassword', val)}
                                    />
                                    <TouchableOpacity 
                                        style={localStyles.eyeIcon}
                                        onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                                    >
                                        <Ionicons 
                                            name={showConfirmPassword ? 'eye-off' : 'eye'} 
                                            size={vs(20)} 
                                            color="#666" 
                                        />
                                    </TouchableOpacity>
                                </View>
                                {errors.confirmPassword && <Text style={localStyles.errorText}>{errors.confirmPassword}</Text>}
                            </View>

                            <View style={localStyles.footerContainer}>
                                {/* Botón de Siguiente */}
                                <TouchableOpacity
                                    style={[localStyles.registerBtn, loading && localStyles.disabled]}
                                    onPress={handleNext}
                                    disabled={loading}
                                >
                                    <Text style={localStyles.registerText}>
                                        {loading ? 'Registrando...' : 'SIGUIENTE'}
                                    </Text>
                                </TouchableOpacity>

                                {/* Aviso de Privacidad */}
                                <View style={localStyles.avisoButton}>
                                        <TouchableOpacity
                                            style={localStyles.aviso}
                                            onPress={() => navigation.navigate('AvisoPrivacidad', {
                                                aceptable: true,
                                                previousScreen: 'register',
                                                aceptado: aceptado,
                                                form: form
                                        })}
                                    >
                                        <Ionicons name={aceptado ? 'checkbox' : 'square-outline' } color={'white'} size={vs(14)}/>
                                        <Text style={localStyles.link}>AVISO DE PRIVACIDAD</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </ScrollView>
                </View>
            </KeyboardAvoidingView>

            {/* Loading Overlay */}
            {loading && (
                <BlurView intensity={20} tint="dark" style={loadingOverlayStyles.loadingOverlay}>
                    <View style={loadingOverlayStyles.loadingCard}>
                        <ActivityIndicator size="large" color="#1698BF" />
                        <Text style={loadingOverlayStyles.loadingText}>Registrando...</Text>
                        <Text style={loadingOverlayStyles.loadingSubtext}>
                            Verificando CURP en el sistema...
                        </Text>
                        <TouchableOpacity
                            style={loadingOverlayStyles.cancelButton}
                            onPress={() => {
                                if (timeoutRef.current) {
                                    clearTimeout(timeoutRef.current);
                                    timeoutRef.current = null;
                                }
                                setLoading(false);
                            }}
                        >
                            <Text style={loadingOverlayStyles.cancelButtonText}>Cancelar</Text>
                        </TouchableOpacity>
                    </View>
                </BlurView>
            )}
        </SafeAreaView>
    );
}

const localStyles = StyleSheet.create({
    scrollContainer: {
        flexGrow: 1,
        paddingTop: vs(50),
        alignItems: 'center',
    },
    wrapper: {
        flex: 1,
        position: 'relative',
    },
    formContainer: {
        width: '80%',
        marginTop: vs(50),
        backgroundColor: 'rgba(218,214,214,0.34)',
        borderRadius: vs(25),
        padding: vs(20),
        marginBottom: vs(20),
    },
    inputsContainer: {
        marginBottom: vs(20),
    },
    title: {
        color: '#fff',
        fontSize: vs(18),
        fontWeight: 'bold',
        textAlign: 'center',
        marginVertical: vs(8),
    },
    label: {
        color: '#fff',
        marginVertical: vs(5),
    },
    input: {
        backgroundColor: '#fff',
        borderRadius: 30,
        paddingHorizontal: vs(20),
        paddingVertical: vs(8),
        color: '#000',
        width: '100%',
    },
    passwordContainer: {
        position: 'relative',
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    passwordInput: {
        backgroundColor: '#fff',
        borderRadius: 30,
        paddingHorizontal: vs(20),
        paddingVertical: vs(8),
        color: '#000',
        flex: 1,
    },
    eyeIcon: {
        position: 'absolute',
        right: vs(15),
        height: '100%',
        justifyContent: 'center',
    },
    errorText: {
        color: '#ff4d4d',
        fontSize: 12,
        marginTop: 3,
        marginLeft: 5,
    },
    footerContainer: {
        alignItems: 'center',
        gap: vs(10),
    },
    registerBtn: {
        backgroundColor: '#02AF14',
        borderRadius: 30,
        paddingVertical: vs(10),
        alignItems: 'center',
        width: vs(125),
    },
    disabled: {
        opacity: 0.6,
    },
    registerText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    avisoButton: {
        alignItems: 'center',
        gap: vs(5),
    },
    link: {
        color: '#ccc',
        fontSize: vs(10),
        textDecorationLine: 'underline',
    },
    aviso: {
        flexDirection: "row",
        gap: 5
    }
});

const loadingOverlayStyles = {
    loadingOverlay: {
        position: 'absolute' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        zIndex: 1000,
    },
    loadingCard: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 25,
        borderRadius: 15,
        alignItems: 'center' as const,
        minWidth: 140,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    loadingText: {
        marginTop: 12,
        color: '#000000',
        fontSize: 15,
        fontWeight: '600' as const,
        textAlign: 'center' as const,
    },
    loadingSubtext: {
        marginTop: 8,
        color: '#666666',
        fontSize: 13,
        textAlign: 'center' as const,
        fontStyle: 'italic' as const,
    },
    cancelButton: {
        marginTop: 15,
        paddingHorizontal: 20,
        paddingVertical: 8,
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    cancelButtonText: {
        color: '#666',
        fontSize: 14,
        fontWeight: '500' as const,
        textAlign: 'center' as const,
    },
};
