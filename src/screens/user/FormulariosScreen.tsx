import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, ActivityIndicator, TouchableOpacity, Animated } from 'react-native';
import { listForms, getFormDetails, submitForm, hasUserSubmittedForm, canUserSubmitForm } from '../../services/formsService';
import { useAuth } from '../../context/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import BaseScreen from '../../components/BaseScreen';
import Header from '../../components/common/Header';
import FormField from '../../components/forms/FormField';
import { useFormValidation, ValidationRules } from '../../hooks/useFormValidation';
import Toast, { useToast } from '../../components/common/Toast';
import {globalTheme} from "../../constants/theme";
import {vs} from "react-native-size-matters";
import { styles } from '../../styles/formulariosStyles';
import { API_URL } from '../../constants/config';

const ICON_MAP: Record<string, string> = {
  'Incapacidades': 'stethoscope',
  'Permisos': 'clock-outline',
  'Vacaciones': 'gift-outline',
  'Justificación de faltas': 'file-document-outline',
  'Actualización de datos': 'account-outline',
};

export default function FormulariosScreen() {
  const { authState } = useAuth();
  const token = authState.token || '';
  const tenant = authState.user?.company_name || 'ts';
  const userId = authState.user?.id;
  const [forms, setForms] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedForm, setSelectedForm] = useState<any | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<any>(null);
  const [autoRefreshInterval, setAutoRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [isAutoRefreshing, setIsAutoRefreshing] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0];


  // Toast para notificaciones
  const { toast, showSuccess, showError, hideToast } = useToast();

  // Configuración de campos para validación
  const getFieldsConfig = (formFields: any[]) => {
    return formFields.map(field => {
      let rules = undefined;

      if (field.required) {
        // Aplicar reglas específicas según el tipo de campo
        switch (field.type) {
          case 'text':
            rules = field.label.toLowerCase().includes('email')
              ? ValidationRules.requiredEmail
              : field.label.toLowerCase().includes('teléfono') || field.label.toLowerCase().includes('telefono')
              ? ValidationRules.requiredPhone
              : ValidationRules.shortText;
            break;
          case 'textarea':
            rules = ValidationRules.longText;
            break;
          case 'email':
            rules = ValidationRules.requiredEmail;
            break;
          case 'phone':
            rules = ValidationRules.requiredPhone;
            break;
          default:
            rules = ValidationRules.required;
        }
      }

      return {
        id: field.id.toString(),
        type: field.type,
        label: field.label,
        rules,
      };
    });
  };

  const {
    values: formValues,
    errors: formErrors,
    updateValue,
    validateAll,
    resetForm,
    initializeValues,
  } = useFormValidation(selectedForm ? getFieldsConfig(selectedForm.fields) : []);

  // Función para cargar formularios (con loading visible)
  const loadForms = async (showLoading = true) => {
    if (!token || !tenant) return;

    if (showLoading) {
      setLoading(true);
    } else {
      setIsAutoRefreshing(true);
    }

    // Tiempo mínimo de visualización para el indicador (1.5 segundos)
    const minDisplayTime = showLoading ? 0 : 1500;
    const startTime = Date.now();

    try {
      const formsData = await listForms(tenant, token);
      setForms(formsData);

      // Si es auto-refresh, asegurar tiempo mínimo de visualización
      if (!showLoading) {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        if (remainingTime > 0) {
          await new Promise(resolve => setTimeout(resolve, remainingTime));
        }
      }
    } catch (e: any) {
      // Solo mostrar error si es la carga inicial
      if (showLoading) {
        setTimeout(() => showError(`Error al cargar formularios: ${e.message}`), 0);
      }

      // Si es auto-refresh y hay error, también esperar el tiempo mínimo
      if (!showLoading) {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        if (remainingTime > 0) {
          await new Promise(resolve => setTimeout(resolve, remainingTime));
        }
      }
    } finally {
      if (showLoading) {
        setLoading(false);
      } else {
        setIsAutoRefreshing(false);
      }
    }
  };

  // Cargar formularios al montar el componente
  useEffect(() => {
    loadForms(true);
  }, [tenant, token]);

  // Auto-refresh cuando estamos en la lista de formularios
  useEffect(() => {
    // Solo activar auto-refresh si estamos en la lista 
    if (!selectedForm && token && tenant) {
      const interval = setInterval(() => {
        loadForms(false);
      }, 10000); // Cada 10 segundos

      setAutoRefreshInterval(interval);

      return () => {
        clearInterval(interval);
        setAutoRefreshInterval(null);
      };
    } else {
      // Si estamos viendo un formulario, limpiar el auto-refresh
      if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        setAutoRefreshInterval(null);
      }
    }
  }, [selectedForm, token, tenant]);

  // Efecto para inicializar valores cuando el formulario y los valores iniciales estén listos
  useEffect(() => {
    if (selectedForm && initialFormValues && selectedForm.fields && selectedForm.fields.length > 0) {
      initializeValues(initialFormValues);

      // Limpiar los valores iniciales para evitar re-ejecuciones
      setInitialFormValues(null);
    }
  }, [selectedForm, initialFormValues, initializeValues]);

  // Cleanup al desmontar el componente
  useEffect(() => {
    return () => {
      if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
      }
    };
  }, []);

  // Animación para el indicador de auto-refresh
  useEffect(() => {
    if (isAutoRefreshing) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isAutoRefreshing]);



  const handleSelectForm = async (id: number) => {
    setLoading(true);
    try {
      const form = await getFormDetails(tenant, token, id);
      // console.log('Form details received:', form);

      // Validar que el formulario tenga la estructura esperada
      if (!form) {
        throw new Error('No se recibieron datos del formulario');
      }

      if (!form.fields || !Array.isArray(form.fields)) {
        // console.warn('Form fields missing or invalid:', form);
        form.fields = []; // Asegurar que fields sea un array
      }

      // Verificar si el usuario ya respondió el formulario
      const hasSubmitted = form.user_submission_status?.has_submitted || false;
      const existingSubmission = form.user_submission_status?.submission || null;

      // Inicializa los valores del formulario
      const initialValues: Record<string, any> = {};

      if (hasSubmitted && existingSubmission && existingSubmission.values) {
        // Si ya respondió, cargar las respuestas existentes
        // console.log('Loading existing submission:', existingSubmission);
        form.fields.forEach((f: any) => {
          const fieldId = f.id.toString();
          const existingValue = existingSubmission.values[fieldId];

          if (existingValue !== undefined) {
            // Para campos checkbox, parsear JSON si es string
            if (f.type === 'checkbox' && typeof existingValue === 'string') {
              try {
                const parsedValue = JSON.parse(existingValue);
                initialValues[fieldId] = Array.isArray(parsedValue) ? parsedValue : [];
              } catch (error) {
                console.warn(`⚠️ Error parseando checkbox value para campo ${fieldId}:`, existingValue, error);
                initialValues[fieldId] = [];
              }
            } else if (['file', 'photo'].includes(f.type)) {
              // Para campos de archivo, procesar el objeto de archivo del servidor
              console.log(`📎 Cargando archivo existente para campo ${fieldId}:`, existingValue);

              if (existingValue && typeof existingValue === 'object' && existingValue.view_url) {
                // Nuevo formato: objeto con información del archivo
                // Usar directamente el view_url que viene del backend
                const baseUrl = API_URL.replace('/ts/api', ''); // Extraer URL base
                const fullUrl = baseUrl + existingValue.view_url;
                console.log(`📎 URL construida para archivo:`, fullUrl);
                initialValues[fieldId] = fullUrl;
              } else if (typeof existingValue === 'string') {
                // Formato anterior: URL directa
                initialValues[fieldId] = existingValue;
              } else {
                // Sin archivo
                initialValues[fieldId] = '';
              }
            } else {
              initialValues[fieldId] = existingValue;
            }
          } else {
            // Valor por defecto si no existe
            if (['checkbox'].includes(f.type)) {
              initialValues[fieldId] = [];
            } else if (['file', 'photo'].includes(f.type)) {
              // Para campos de archivo sin valor, usar string vacío
              initialValues[fieldId] = '';
            } else {
              initialValues[fieldId] = '';
            }
          }
        });
      } else {
        // Si no ha respondido, usar valores vacíos
        form.fields.forEach((f: any) => {
          // Los campos de texto, número, email, phone usan string vacío
          // Los campos de checkbox, radio, select múltiple usan array vacío
          if (['text', 'textarea', 'number', 'email', 'phone'].includes(f.type)) {
            initialValues[f.id.toString()] = '';
          } else if (['checkbox'].includes(f.type)) {
            initialValues[f.id.toString()] = [];
          } else if (['file', 'photo'].includes(f.type)) {
            // Para campos de archivo, usar string vacío
            initialValues[f.id.toString()] = '';
          } else {
            // Para select y radio, usar string vacío
            initialValues[f.id.toString()] = '';
          }
        });
      }

      // console.log('Initial values:', initialValues);
      // console.log('Has submitted:', hasSubmitted);

      // Log específico para archivos
      const fileFields = form.fields.filter((f: any) => ['file', 'photo'].includes(f.type));
      if (fileFields.length > 0) {
        // console.log('📎 Campos de archivo encontrados:', fileFields.map((f: any) => ({
        //   id: f.id,
        //   type: f.type,
        //   label: f.label,
        //   value: initialValues[f.id.toString()]
        // })));
      }

      // Pequeña pausa para una transición más suave
      await new Promise(resolve => setTimeout(resolve, 300));

      setSelectedForm(form);

      // Guardar los valores iniciales para usar en useEffect
      setInitialFormValues(initialValues);
    } catch (e: any) {
      // console.error('❌ Error loading form:', e);
      showError(`Error al cargar el formulario: ${e.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (fieldId: string, value: any) => {
    // console.log('📝 Input change - Field ID:', fieldId, 'New value:', value, 'Type:', typeof value);
    updateValue(fieldId, value);
  };

  const handleSubmit = async () => {
    if (!selectedForm) return;

    // Verificar si ya fue enviado
    if (hasUserSubmittedForm(selectedForm)) {
      showError('Este formulario ya ha sido enviado');
      return;
    }

    // Verificar si puede enviar
    if (!canUserSubmitForm(selectedForm)) {
      showError('No tienes permisos para enviar este formulario');
      return;
    }

    // Validar formulario antes de enviar
    if (!validateAll()) {
      showError('Por favor corrige los errores en el formulario');
      return;
    }

    setSubmitting(true);
    try {
/*      console.log('=== SUBMITTING FORM ===');
      console.log('Tenant:', tenant);
      console.log('Token:', token ? 'Present' : 'Missing');
      console.log('Form ID:', selectedForm.id);
      console.log('Form Values:', formValues);
      console.log('Selected Form:', selectedForm);
      console.log('========================');*/

      await submitForm(tenant, token, selectedForm.id, formValues);
      showSuccess('¡Formulario enviado correctamente!');

      // Recargar los detalles del formulario para actualizar el estado
      setTimeout(async () => {
        try {
          const updatedForm = await getFormDetails(tenant, token, selectedForm.id);
          setSelectedForm(updatedForm);
          // No resetear el form para que el usuario vea sus respuestas
        } catch (error) {
          // console.error('Error reloading form:', error);
          // Si hay error recargando, volver a la lista
          setSelectedForm(null);
          resetForm();
        }
      }, 1500);
    } catch (e: any) {
      // console.error('Submit error:', e);

      // Manejar error de formulario ya enviado
      try {
        const errorData = JSON.parse(e.message);
        if (errorData.type === 'ALREADY_SUBMITTED') {
          showError('Este formulario ya ha sido enviado anteriormente');
          // Recargar el formulario para mostrar el estado actualizado
          setTimeout(async () => {
            try {
              const updatedForm = await getFormDetails(tenant, token, selectedForm.id);
              setSelectedForm(updatedForm);
            } catch (reloadError) {
              // console.error('Error reloading form after duplicate:', reloadError);
            }
          }, 1000);
        } else {
          showError(`Error al enviar el formulario: ${errorData.message}`);
        }
      } catch (parseError) {
        // Si no es un error JSON estructurado, mostrar el mensaje original
        showError(`Error al enviar el formulario: ${e.message}`);
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <BaseScreen
          scroll={false}
          navColor={'#ffffff'}
          chat={true}
      >
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1698BF" />
          <Text style={styles.loadingText}>
            {selectedForm ? 'Cargando formulario...' : 'Cargando formularios...'}
          </Text>
        </View>
      </BaseScreen>
    );
  }

  if (selectedForm) {
    // console.log('Rendering selectedForm:', selectedForm);

    // Verificar el estado de envío
    const hasSubmitted = hasUserSubmittedForm(selectedForm);
    const canSubmit = canUserSubmitForm(selectedForm);
    const submissionInfo = selectedForm.user_submission_status?.submission;

    return (
      <>
        <BaseScreen
            navColor={globalTheme.navigation_light}
        >
          <Header />
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>{selectedForm.name || 'Formulario'}</Text>
            <Text style={styles.formDescription}>
              {selectedForm.description || 'Complete los siguientes campos'}
            </Text>

            {/* Mostrar estado de envío */}
            {hasSubmitted && (
              <View style={styles.submittedBanner}>
                <Ionicons name="checkmark-circle" size={24} color="#10B981" />
                <View style={styles.submittedTextContainer}>
                  <Text style={styles.submittedTitle}>Formulario Enviado</Text>
                  <Text style={styles.submittedSubtitle}>
                    Enviado el {submissionInfo?.submitted_at ?
                      new Date(submissionInfo.submitted_at).toLocaleDateString('es-ES', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      }) : 'fecha no disponible'}
                  </Text>
                </View>
              </View>
            )}

            {selectedForm.fields && selectedForm.fields.length > 0 ? (
              selectedForm.fields.map((f: any) => {
                const fieldId = f.id.toString();
                // console.log('Rendering field:', f);

                // Log específico para campos de archivo
                if (['file', 'photo'].includes(f.type)) {
                  // console.log(`📎 Renderizando campo de archivo ${f.type}:`, {
                  //   id: fieldId,
                  //   label: f.label,
                  //   value: formValues[fieldId],
                  //   disabled: hasSubmitted
                  // });
                }

                // Procesar las opciones para campos select, radio y checkbox
                let processedOptions: any[] = [];
                if (f.options && typeof f.options === 'string' && f.options.trim() !== '') {
                  // Convertir string de opciones separadas por coma a array de objetos
                  processedOptions = f.options.split(',').map((option: string) => ({
                    label: option.trim(),
                    value: option.trim()
                  }));
                } else if (Array.isArray(f.options)) {
                  // Si ya es un array, usarlo directamente
                  processedOptions = f.options;
                }

                return (
                  <FormField
                    key={fieldId}
                    id={fieldId}
                    type={f.type === 'text' ? 'textarea' : f.type}
                    label={f.label || `Campo ${fieldId}`}
                    value={formValues[fieldId]}
                    placeholder={f.placeholder || `Escribe tu ${f.label?.toLowerCase() || 'respuesta'}`}
                    required={f.required}
                    onChangeValue={handleInputChange}
                    error={formErrors[fieldId]}
                    options={processedOptions}
                    disabled={hasSubmitted} // Deshabilitar si ya fue enviado
                    userId={userId} // Para construir URLs de archivos del servidor
                  />
                );
              })
            ) : (
              <View style={styles.noFieldsContainer}>
                <Text style={styles.noFieldsText}>
                  Este formulario no tiene campos configurados
                </Text>
              </View>
            )}

            {/* Solo mostrar botón de envío si hay campos y no ha sido enviado */}
            {selectedForm.fields && selectedForm.fields.length > 0 && !hasSubmitted && canSubmit && (
              <TouchableOpacity
                style={[
                  styles.submitButton,
                  {
                    opacity: submitting ? 0.8 : 1,
                    backgroundColor: submitting ? globalTheme.gradient[0] : globalTheme.button_ok,
                  }
                ]}
                onPress={handleSubmit}
                disabled={submitting}
              >
                {submitting ? (
                  <View style={styles.submittingContainer}>
                    <ActivityIndicator color="#fff" size="small" />
                    <Text style={[styles.submitButtonText, { marginLeft: 8 }]}>
                      Enviando...
                    </Text>
                  </View>
                ) : (
                  <Text style={styles.submitButtonText}>Enviar Respuestas</Text>
                )}
              </TouchableOpacity>
            )}

            {/* Mostrar mensaje si no puede enviar */}
            {!canSubmit && !hasSubmitted && (
              <View style={styles.cannotSubmitContainer}>
                <Ionicons name="information-circle" size={24} color="#F59E0B" />
                <Text style={styles.cannotSubmitText}>
                  Este formulario no está disponible para envío en este momento
                </Text>
              </View>
            )}

            {/* Botón de volver siempre visible */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => {
                setSelectedForm(null);
                resetForm();
              }}
              disabled={submitting}
            >
              <Ionicons name={'arrow-back'} color={'#FFF'} size={vs(16)}/>
              <Text style={styles.backButtonText}> Volver a la lista</Text>
            </TouchableOpacity>
          </View>
        </BaseScreen>

        <Toast
          visible={toast.visible}
          message={toast.message}
          type={toast.type}
          onHide={hideToast}
        />
      </>
    );
  }

  return (
    <>
      <BaseScreen
          navColor={globalTheme.navigation_light}
          chat={true}
          scroll={false}
      >
        <Header />
        <View style={styles.container}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: -15 }}>
            <Text style={styles.title}>Formularios</Text>
            <Animated.View style={{
              opacity: fadeAnim,
              transform: [{ scale: fadeAnim }],
              marginTop: -4
            }}>
              {isAutoRefreshing && (
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: globalTheme.gradient[1] + '20',
                  paddingHorizontal: 12,
                  paddingVertical: 4,
                  borderRadius: 20,
                  borderWidth: 1,
                  borderColor: globalTheme.gradient[1] + '40'
                }}>
                  <ActivityIndicator size="small" color={globalTheme.gradient[1]} />
                  <Text style={{
                    fontSize: 13,
                    color: globalTheme.gradient[1],
                    marginLeft: 8,
                    fontWeight: '500'
                  }}>
                    Actualizando...
                  </Text>
                </View>
              )}
            </Animated.View>
          </View>
          <FlatList
            data={forms}
            keyExtractor={item => item.id.toString()}
            renderItem={({ item }) => {
              const iconName = (ICON_MAP[item.name] || 'file-document-outline') as any;
              return (
                <TouchableOpacity style={styles.item} onPress={() => handleSelectForm(item.id)}>
                  <View style={styles.iconBox}>
                    <MaterialCommunityIcons name={iconName} size={28} color={globalTheme.gradient[1]} />
                  </View>
                  <Text style={styles.itemText}>{item.name}</Text>
                  <Ionicons name="chevron-forward" size={24} color={globalTheme.gradient[1]} style={styles.chevron} />
                </TouchableOpacity>
              );
            }}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="document-outline" size={64} color="#ccc" />
                <Text style={styles.emptyText}>No hay formularios disponibles</Text>
                <Text style={styles.emptySubtext}>Los formularios aparecerán aquí cuando estén disponibles</Text>
              </View>
            }
          />
        </View>
      </BaseScreen>

      <Toast
        visible={toast.visible}
        message={toast.message}
        type={toast.type}
        onHide={hideToast}
      />
    </>
  );
}


