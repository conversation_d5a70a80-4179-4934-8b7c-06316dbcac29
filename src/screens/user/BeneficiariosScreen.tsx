// screens/BeneficiariosScreen.tsx

import React, {useEffect, useState} from 'react';
import {
    View,
    Text,
    ActivityIndicator,
    TouchableOpacity,
    Alert
} from 'react-native';
import BaseScreen from '../../components/BaseScreen';
import {useAuth} from '../../context/AuthContext';
import {getBeneficiarios, deleteBeneficiario} from '../../services/beneficiarioService';
import {showErrorAlert, showSuccessAlert} from '../../utils/alertUtils';
import {Ionicons} from '@expo/vector-icons';
import BeneficiarioCard from '../../components/beneficiarios/BeneficiarioCard';
import Header from "../../components/common/Header";
import {nuevoBeneficiarioStyles as styles} from "../../styles/nuevoBeneficiarioStyles";
import {mvs, vs} from "react-native-size-matters";
import {globalTheme} from "../../constants/theme";


export default function BeneficiariosScreen({navigation}: any) {
    const {authState} = useAuth();
    const [beneficiarios, setBeneficiarios] = useState([]);
    const [loading, setLoading] = useState(true);

    const token = authState.token;

    const fetchData = async () => {
        try {
            const userId = authState.user?.user_id;
            if (!userId) throw new Error('Usuario no identificado');

            console.log('📋 BeneficiariosScreen - Obteniendo beneficiarios para userId:', userId);
            const response = await getBeneficiarios(userId);

            if (response.error) {
                console.error('❌ Error al obtener beneficiarios:', response.msg);
                showErrorAlert({ error: { code: 'FETCH_BENEFICIARIOS', message: response.msg } });
            } else {
                const beneficiariosArray = Object.values(response.data);
                console.log('✅ Beneficiarios obtenidos:', beneficiariosArray.length);
                console.log('📸 Fotos en beneficiarios:', beneficiariosArray.map((b: any) => ({
                    nombre: b.name,
                    tieneFoto: !!b.photo,
                    foto: b.photo ? b.photo.substring(0, 50) + '...' : 'NO FOTO'
                })));
                setBeneficiarios(beneficiariosArray);
            }
        } catch (error: any) {
            console.error('❌ Error inesperado al obtener beneficiarios:', error);
            showErrorAlert({ error: { code: 'UNEXPECTED_ERROR', message: error.message } });
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (beneficiarioId: number) => {
        const userId = authState.user?.user_id;
        if (!userId || !token) {
            showErrorAlert({ error: { code: 'AUTH_ERROR', message: 'Usuario no autenticado' } });
            return;
        }

        const response = await deleteBeneficiario(userId, beneficiarioId, token);

        if (response.error) {
            showErrorAlert({ error: { code: 'DELETE_ERROR', message: response.msg } });
        } else {
            setBeneficiarios(prev => prev.filter(b => b.id !== beneficiarioId));
            showSuccessAlert('Beneficiario eliminado', 'Se eliminó correctamente.');
        }
    };

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            console.log('🔄 BeneficiariosScreen - Pantalla enfocada, recargando datos...');
            setLoading(true);
            fetchData();
        });

        console.log('🚀 BeneficiariosScreen - Carga inicial');
        fetchData();

        return unsubscribe;
    }, [navigation]);

    return (
        <BaseScreen navColor={globalTheme.gradient[1]}>
            <Header />

            <View style={styles.container}>
                <Text style={styles.header}>BENEFICIARIOS</Text>

                <TouchableOpacity
                    style={styles.agregar}
                    onPress={() => navigation.navigate('NuevoBeneficiario')}
                >
                    <Text style={{color: "#FFF"}}>Agregar beneficiario</Text>
                    <Ionicons name="add-circle-outline" size={24} color="white" />
                </TouchableOpacity>

                {loading ? (
                    <ActivityIndicator size="large" color="#00aa88" />
                ) : (
                    <View>
                        {beneficiarios.length === 0 ? (
                            <Text style={{ textAlign: 'center', marginVertical: vs(20), color: "#FFF" }}>
                                No hay beneficiarios registrados.
                            </Text>
                        ) : (
                            beneficiarios.map((item: any) => (
                                <BeneficiarioCard
                                    key={item.id}
                                    nombre={`${item.name} ${item.last_name}`}
                                    relacion={item.kinship}
                                    fechaNacimiento={item.birthday}
                                    foto={item.photo}
                                    onEdit={() => navigation.navigate('NuevoBeneficiario', { beneficiario: item })}
                                    onDelete={() =>
                                        Alert.alert(
                                            '¿Eliminar beneficiario?',
                                            'Esta acción no se puede deshacer.',
                                            [
                                                { text: 'Cancelar', style: 'cancel' },
                                                {
                                                    text: 'Eliminar',
                                                    style: 'destructive',
                                                    onPress: () => handleDelete(item.id),
                                                },
                                            ]
                                        )
                                    }
                                    kinship={item.kinship}
                                />
                            ))
                        )}
                    </View>
                )}

                <TouchableOpacity style={styles.regresar} onPress={() => navigation.goBack()}>
                    <Ionicons name="reload" size={mvs(25, 0.5)} color="#fff"/>
                    <Text style={styles.regresarText}>Regresar</Text>
                </TouchableOpacity>
            </View>
        </BaseScreen>
    );
}
