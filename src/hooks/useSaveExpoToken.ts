import {use, useState} from "react";
import axios from "axios";
import {API_URL} from "../constants/config";



export default function useSaveExpoToken(){

    const [loadingSaveToken, setLoading] = useState(false);
    const [errorSaveToken, setError] = useState<string | null>(null);

    const saveToken= async (
        token_device: string,
        token: string,
        userId: string) => {

        console.log('📱 saveToken - Iniciando guardado de token');
        console.log('🔧 Parámetros:', {
            token_device: token_device ? 'PRESENTE' : 'FALTANTE',
            token: token ? 'PRESENTE' : 'FALTANTE',
            userId
        });

        // Validar parámetros obligatorios
        if (!token_device || !token || !userId) {
            console.error('❌ Faltan parámetros obligatorios para saveToken');
            setError('Faltan parámetros obligatorios');
            return;
        }

        setLoading(true);

        try {
            const endpoint = `${API_URL}/users/${userId}/device-token`;
            const payload = { device_token: token_device };

            console.log('🌐 Endpoint:', endpoint);
            console.log('📤 Payload:', payload);

            const { data } = await axios.post(
                endpoint,
                payload,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                }
            );

            console.log('✅ Token guardado exitosamente:', data);

        } catch (err: any) {
            console.error('❌ Error al guardar token:');
            console.error('🔍 Error completo:', err);
            console.error('📡 Response status:', err.response?.status);
            console.error('📡 Response data:', err.response?.data);
            console.error('💬 Error message:', err.message);

            setError(err.response?.data?.message || err.message || 'Error al guardar token');
        } finally {
            setLoading(false)
        }
    }

    return {
        loadingSaveToken,
        errorSaveToken,
        saveToken
    }

}
