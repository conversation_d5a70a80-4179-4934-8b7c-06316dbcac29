import {useCallback, useState} from "react";
import axios from "axios";
import {API_URL} from "../constants/config";
import {useAuth} from "../context/AuthContext";

interface ApiResponse {
    link: string;
    code: number;
    error?: {
        code: string;
        message: string;
    };
}

interface Props {
    setLink: (link: string) => void;
}

export default function useCredentialQr({setLink}: Props) {
    const { authState } = useAuth();
    const [loading, setLoading] = useState(false);
    const [errorCode, setErrorCode] = useState<string | null>(null);

    const fetchQr = useCallback(async (email: string) => {
        setLoading(true);
        setErrorCode(null);

        try {
            const { data } = await axios.get<ApiResponse>(
                `${API_URL}/credential/token`,
                {
                    params: { email },
                    headers: { Authorization: `Bearer ${authState.token}` },
                }
            );

            if (data.code === 200) {
                setLink(data.link);
            } else if (data.error) {
                setErrorCode(data.error.code);
            }
        } catch (err: any) {
        } finally {
            setLoading(false);
        }
    }, [authState.token, setLink]);

    return {
        loadingQr: loading,
        errorQr: errorCode,
        fetchQr
    };
}
