import {useEffect, useState, useCallback, use} from "react";
import { useAuth } from "../context/AuthContext";
import axios from "axios";
import { API_URL } from "../constants/config";
import {ApiPublishMessage, ApiSubscribe} from "../types/ChatTypes";

const fetchChatCredentials = async (userId: string, token: string) => {
    try {
        const { data } = await axios.get<ApiSubscribe>(
            `${API_URL}/users/${userId}/subscribe`,
            {
                headers: { Authorization: `Bearer ${token}` },
            }
        );
        return data;
    } catch (error) {
        console.error("Error fetching chat credentials:", error);
        throw error;
    }
};

export const publishMessage = async (
    conversationId: number,
    userId: number,
    token: string,
    content: string
): Promise<ApiPublishMessage> => {

    console.log(conversationId, userId, token, content)

    try {
        const { data } = await axios.post(
            `${API_URL}/users/${userId}/publish`,
            {
                conversationId: conversationId,
                content: content
            },
            {
                headers: { Authorization: `Bearer ${token}` }
            }
        );

        return {
            id: data.conversation,
            msj: "publicado con éxito",
            code: 200
        };
    } catch (e) {
        console.log(e);
        return {
            id: null,
            msj: 'error al publicar',
            code: 401
        };
    }
}


export function useChatCredentials(setMessages: (messages: any[]) => void) {
    const { authState } = useAuth();
    const [conversation, setConversation] = useState<object | null>(null);
    const [topic, setTopic] = useState<string | null>(null);
    const [authorization, setAuthorization] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Fetch chat credentials when the component mounts
    useEffect(() => {
        const getChatCredentials = async () => {
            if (!authState?.user?.user_id || !authState.token) return;

            setLoading(true);
            setError(null);

            try {
                const data = await fetchChatCredentials(authState.user.user_id, authState.token);
                setConversation(data.conversation);
                setMessages(data.messages);
                setTopic(data.topic);
                setAuthorization(data.authorization);

                console.log('fetch',data.messages)
            } catch (error) {
                setError("Failed to fetch chat credentials.");
            } finally {
                setLoading(false);
            }
        };

        getChatCredentials();
    }, [authState?.user?.user_id, authState.token]);

    return { conversation, topic, authorization, loading, error };
}
