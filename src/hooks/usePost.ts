import { useEffect, useState } from "react";
import axios from "axios";
import { API_URL } from "../constants/config";
import { normalizeImageUrl } from "../utils/imageUtils";

interface Props {
    company_id: string;
    token: string;
    start_date: string;  // Fecha de inicio para filtrar posts (YYYY-MM-DD)
    end_date: string;    // Fecha de fin para filtrar posts (YYYY-MM-DD)
    amount: number;
    setPost: (post: Post[]) => void;
}

/**
 * Representa un post de redes sociales.
 * Un post es visible si su rango de fechas (start_date a end_date) se superpone
 * con el rango de fechas solicitado. Es decir:
 * - Si un post inicia el día 1 y termina el día 3, será visible en los días 1, 2 y 3
 * - Si un post inicia hoy y termina mañana, será visible tanto hoy como mañana
 */
interface Post {
    "id": number,
    "title": string,
    "description": string,
    "image": string,
    "url": string,
    "platform": string,
    "start_date": string,  // Fecha de inicio del post (YYYY-MM-DD)
    "end_date": string,    // Fecha de fin del post (YYYY-MM-DD)
}

interface ApiResponse {
    social_media: Post[],
    code: number
}

export function usePost({ company_id, token, start_date, end_date, amount, setPost }: Props) {
    const [loadingPost, setLoading] = useState(true);
    const [errorPost, setError] = useState<string | null>(null);

    useEffect(() => {
        console.log("[usePost] Iniciando efecto");
        console.log("[usePost] Parámetros recibidos:", {
            company_id,
            token: token ? "TOKEN PRESENTE" : "TOKEN AUSENTE",
            start_date,
            end_date,
            amount
        });

        // Validar que tenemos los parámetros necesarios
        if (!company_id || !token) {
            console.warn("[usePost] Faltan parámetros obligatorios:", {
                company_id: company_id ? "PRESENTE" : "FALTANTE",
                token: token ? "PRESENTE" : "FALTANTE"
            });
            setLoading(false);
            setError("Faltan parámetros obligatorios para cargar posts");
            return;
        }

        setLoading(true);
        setError(null);

        const apiUrl = `${API_URL}/social-media`;
        console.log("[usePost] URL de la API:", apiUrl);

        const requestParams = {
            company_id: String(company_id), // Asegurar que sea string
            start_date,
            end_date,
            amount
        };
        console.log("[usePost] Parámetros de la solicitud:", requestParams);

        axios.get<ApiResponse>(apiUrl, {
            params: requestParams,
            headers: { Authorization: `Bearer ${token}` },
        })
            .then(response => {
                console.log("[usePost] Respuesta recibida:", response);
                console.log("[usePost] Código de estado:", response.status);

                if (response.data && response.data.social_media) {
                    console.log("[usePost] Datos recibidos:", response.data.social_media);
                    console.log("[usePost] Número de posts:", response.data.social_media.length);

                    // Normalizar URLs de imágenes y analizar cada post del backend
                    const normalizedPosts = response.data.social_media.map((post: any) => {
                        if (post.image) {
                            const originalImage = post.image;
                            const normalizedImage = normalizeImageUrl(post.image);
                            console.log(`[usePost] 🔧 Normalizando imagen:`, {
                                original: originalImage,
                                normalized: normalizedImage,
                                changed: originalImage !== normalizedImage
                            });
                            return { ...post, image: normalizedImage };
                        }
                        return post;
                    });

                    // Analizar cada post normalizado
                    normalizedPosts.forEach((post: any, index: number) => {
                        console.log(`[usePost] Post ${index}:`, {
                            id: post.id,
                            title: post.title,
                            image: post.image,
                            imageType: typeof post.image,
                            imageLength: post.image?.length,
                            platform: post.platform,
                            imageStartsWith: post.image ? post.image.substring(0, 30) + '...' : 'NO IMAGE',
                            isAbsoluteUrl: post.image && (post.image.startsWith('http://') || post.image.startsWith('https://')),
                            hasValidExtension: post.image && (
                                post.image.endsWith('.jpg') ||
                                post.image.endsWith('.jpeg') ||
                                post.image.endsWith('.png')
                            )
                        });

                        // Verificar si la URL de imagen es accesible
                        if (post.image) {
                            console.log(`[usePost] 🔍 Verificando accesibilidad de imagen ${index}:`, post.image);
                            fetch(post.image, { method: 'HEAD' })
                                .then(response => {
                                    console.log(`[usePost] ✅ Imagen ${index} accesible:`, {
                                        status: response.status,
                                        contentType: response.headers.get('content-type'),
                                        url: post.image
                                    });
                                })
                                .catch(error => {
                                    console.error(`[usePost] ❌ Imagen ${index} NO accesible:`, {
                                        error: error.message,
                                        url: post.image
                                    });
                                });
                        }
                    });

                    setPost(normalizedPosts);
                } else {
                    console.warn("[usePost] Respuesta inesperada:", response.data);
                    setError("Estructura de respuesta inesperada");
                }
            })
            .catch(err => {
/*                console.error("[usePost] Error en la solicitud:", err);*/

                if (err.response) {
                    // Error con respuesta del servidor
/*                    console.error("[usePost] Detalles del error:", {
                        status: err.response.status,
                        data: err.response.data,
                        headers: err.response.headers
                    });*/
                    setError(`Error ${err.response.status}: ${err.response.data?.message || err.message}`);
                } else if (err.request) {
                    // La solicitud fue hecha pero no hubo respuesta
/*                    console.error("[usePost] No se recibió respuesta del servidor");*/
                    setError("No se pudo conectar con el servidor");
                } else {
                    // Error al configurar la solicitud
/*                    console.error("[usePost] Error de configuración:", err.message);*/
                    setError(err.message || "Error en la configuración de la solicitud");
                }
            })
            .finally(() => {
/*                console.log("[usePost] Finalizando solicitud");*/
                setLoading(false);
            });

    }, [company_id, token, start_date, end_date, amount, setPost]);

/*    console.log("[usePost] Estado actual:", { loadingPost, errorPost });*/
    return { loadingPost, errorPost };
}