import {useEffect, useState} from "react";
import axios from "axios";
import {API_URL} from "../constants/config";



interface Benefit {
    id: string;
    title: string;
    description: string;
    validity_start_date: string;
    validity_end_date: string;
    image: string;
}

interface ApiResponse {
    code:   number;
    benefits: Benefit[];
    pagination: {
        page: number,
        per_page: number,
        total_items: number,
        total_pages: number,
        has_next_page: boolean,
        has_previous_page: boolean,
    }
}

interface Props {
    companyId: string;
    page: number;
    token: string;
    setPagination?: (value: boolean) => void;
    setBenefits: (benefit: (prevBenefits) => any[]) => void;
}

export function useBenefits({companyId, page, token, setBenefits, setPagination,}: Props){
    const [loadingBenefits, setLoading] = useState(true);
    const [errorBenefits, setError] = useState<string | null>(null);

    useEffect(() => {
        console.log('🎁 useBenefits - Iniciando carga de beneficios');
        console.log('🔧 Parámetros:', {
            companyId,
            page,
            token: token ? 'PRESENTE' : 'FALTANTE'
        });

        // Validar parámetros obligatorios
        if (!companyId || !token) {
            console.error('❌ Faltan parámetros obligatorios para beneficios:', {
                companyId: companyId ? 'PRESENTE' : 'FALTANTE',
                token: token ? 'PRESENTE' : 'FALTANTE'
            });
            setError('Faltan parámetros obligatorios');
            setLoading(false);
            return;
        }

        setLoading(true);

        const endpoint = `${API_URL}/benefits`;
        const params = {
            company_id: companyId,
            per_page: 6,
            page
        };

        console.log('🌐 Endpoint:', endpoint);
        console.log('📤 Parámetros de solicitud:', params);

        axios.get<ApiResponse>(endpoint, {
            params,
            headers: { Authorization: `Bearer ${token}` },
        })
            .then(({data}) => {
                console.log('✅ Respuesta de beneficios recibida:', data);
                console.log('📊 Código de respuesta:', data.code);
                console.log('🎁 Número de beneficios:', data.benefits?.length || 0);

                if (data.benefits && data.benefits.length > 0) {
                    console.log('📋 Beneficios recibidos:');
                    data.benefits.forEach((benefit, index) => {
                        console.log(`  ${index + 1}. ${benefit.title} (ID: ${benefit.id})`);
                        console.log(`     Imagen: ${benefit.image}`);
                        console.log(`     Vigencia: ${benefit.validity_start_date} - ${benefit.validity_end_date}`);
                    });
                } else {
                    console.warn('⚠️ No se recibieron beneficios del backend');
                }

                setBenefits(prevBenefits => {
                    console.log('🔄 Beneficios anteriores:', prevBenefits.length);
                    const beneficiosExistentes = new Set(prevBenefits.map(benefit => benefit.id));
                    const nuevosBenefits = data.benefits.filter(benefit => !beneficiosExistentes.has(benefit.id));
                    console.log('🆕 Nuevos beneficios a agregar:', nuevosBenefits.length);
                    const totalBenefits = [...prevBenefits, ...nuevosBenefits];
                    console.log('📊 Total de beneficios después de actualizar:', totalBenefits.length);
                    return totalBenefits;
                });

                if (setPagination) {
                    console.log('📄 Paginación - Tiene siguiente página:', data.pagination?.has_next_page);
                    setPagination(data.pagination.has_next_page);
                }
            })
            .catch(err => {
                console.error('❌ Error al cargar beneficios:');
                console.error('🔍 Error completo:', err);
                console.error('📡 Response status:', err.response?.status);
                console.error('📡 Response data:', err.response?.data);
                console.error('💬 Error message:', err.message);

                setError(err.response?.data?.message || err.message || "Error cargando beneficios");
            })
            .finally(() => {
                console.log('🏁 Finalizando carga de beneficios');
                setLoading(false);
            })
    }, [page]);

    return {loadingBenefits, errorBenefits};

}