import {vs} from "react-native-size-matters";


const light = {
    background: "#FFF",
    gradient: [
        "#385927",
        "#02415F"
    ],

    text_head: "#fff",
    text_light: "#ccc",
    text_contrast: "#000",
    text_title: "#2D2769",

    text_placeholder: "#aaa",

    text_special: '#D5FFD9',
    text_error: '#ff4d4d',

    input: "#fff",

    tabs: "#FFF",
    tabs_active: "#008E0E",
    tabs_inactive: "#1698BF",

    button_ok: "#008E0E",
    button_bad: "#CB4A4A",
    button_light: "#23A5D7",

    container_translucent: 'rgba(255,255,255,0.34)',
    container_light: "#ffffff",
    container_medium: "#8797B8",
    container_dark: "#38556B",

    container_contrast: '#67B101',

    navigation_light: "#ffffff",
    navigation_dark: "#02415F",
};

export {light as globalTheme}

export const borderRadius = vs(25);


