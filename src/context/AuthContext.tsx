import React, {createContext, useContext, useEffect, useState} from 'react';
import * as SecureStore from 'expo-secure-store';
import axios from 'axios';
import {API_URL} from '../constants/config';
import {getProfile, registerUser} from '../services/authService';
import type {RegisterPayload} from '../services/authService';
import {ImageSourcePropType} from "react-native";
import {showErrorAlert} from '../utils/alertUtils';

interface AuthContextProps {
    authState: AuthState;
    setAuthState: React.Dispatch<React.SetStateAction<AuthState>>;
    login: (email: string, password: string) => Promise<any>;
    register: (payload: RegisterPayload) => Promise<any>;
    logout: () => Promise<void>;
    loginForFinger: (email: string, password: string) => Promise<any>;
}

interface AuthState {
    token: string | null;
    user: {
        photo: ImageSourcePropType | undefined;
        company_name: any;
        email: string;
        phone_number: string;
        last_name: string;
        name: string;
        user_id: any;
        company_id: any;
        employee_number: any;
        curp: string;
        id: number;
    } | null;
    authenticated: boolean;
    loading: boolean;
    biometrics: string;
    pastUser: string;
    pastDeviceToken: string;
}

const TOKEN_KEY = 'token';
const USER_KEY = 'user';
const EMAIL_KEY = 'email';
const PASSWORD_KEY = 'password';
const BIOMETRICS_KEY = 'biometrics';
const PAST_USER_KEY = 'pastUser'
const PAST_DEVICE_TOKEN_KEY = 'pastDeviceToken';

const AuthContext = createContext<AuthContextProps>({} as AuthContextProps);

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({children}: { children: React.ReactNode }) => {
    const [authState, setAuthState] = useState<AuthState>({
        token: null,
        user: null,
        authenticated: false,
        loading: true,
        biometrics: "false",
        pastUser: null,
        pastDeviceToken: null
    });

    // Función logout (definida antes del interceptor para que esté disponible)
    const logout = async () => {
        // Guardar el pastUser antes de cambiar el estado
        const pastUser = authState.user?.user_id;
        if (pastUser) {
            await SecureStore.setItemAsync(PAST_USER_KEY, String(pastUser));
        }

        // Borrar la sesión actual
        await SecureStore.deleteItemAsync(TOKEN_KEY);
        await SecureStore.deleteItemAsync(USER_KEY);
        const biometricFlag = await SecureStore.getItemAsync(BIOMETRICS_KEY);
        const pastToken = await SecureStore.getItemAsync(PAST_DEVICE_TOKEN_KEY);

        // Eliminar el Authorization header de Axios
        delete axios.defaults.headers.common['Authorization'];

        // Actualizar el estado
        setAuthState({
            token: null,
            user: null,
            authenticated: false,
            loading: false,
            biometrics: biometricFlag,
            pastUser: pastUser,
            pastDeviceToken: pastToken
        });
    };

    // Configurar interceptor de axios para manejar errores de JWT
    useEffect(() => {
        const responseInterceptor = axios.interceptors.response.use(
            (response) => response, // Si la respuesta es exitosa, la devolvemos tal como está
            async (error) => {
                const originalRequest = error.config;

                console.log('🔍 Interceptor de axios - Error capturado:', {
                    status: error.response?.status,
                    url: originalRequest?.url,
                    method: originalRequest?.method
                });

                // Verificar si es un error de JWT expirado
                if (error.response?.status === 401) {
                    const errorMessage = error.response?.data?.message || error.response?.data?.error?.message || '';
                    const errorCode = error.response?.data?.error?.code || '';

                    console.log('🔍 Error 401 detectado:', {
                        message: errorMessage,
                        code: errorCode,
                        fullResponse: error.response?.data
                    });

                    // Detectar diferentes tipos de errores de JWT
                    const isJWTError = errorMessage.toLowerCase().includes('jwt') ||
                                     errorMessage.toLowerCase().includes('token') ||
                                     errorMessage.toLowerCase().includes('expired') ||
                                     errorMessage.toLowerCase().includes('unauthorized') ||
                                     errorMessage.toLowerCase().includes('expired jwt token') ||
                                     errorCode.toLowerCase().includes('jwt') ||
                                     errorCode.toLowerCase().includes('token') ||
                                     errorCode.toLowerCase().includes('expired');

                    console.log('🔍 Análisis de error JWT:', {
                        isJWTError,
                        messageIncludes: {
                            jwt: errorMessage.toLowerCase().includes('jwt'),
                            token: errorMessage.toLowerCase().includes('token'),
                            expired: errorMessage.toLowerCase().includes('expired'),
                            unauthorized: errorMessage.toLowerCase().includes('unauthorized'),
                            expiredJwtToken: errorMessage.toLowerCase().includes('expired jwt token')
                        }
                    });

                    if (isJWTError) {
                        console.log('🔐 Token expirado detectado, cerrando sesión...');

                        // Cerrar sesión automáticamente
                        await logout();

                        // Mostrar mensaje amigable
                        showErrorAlert({
                            error: {
                                code: 'SESSION_EXPIRED',
                                message: 'Tu sesión ha expirado. Por favor inicia sesión nuevamente.'
                            }
                        });

                        // Rechazar la promesa con un error personalizado
                        return Promise.reject({
                            ...error,
                            customMessage: 'Sesión expirada'
                        });
                    }
                }

                // Si no es un error de JWT, devolver el error original
                return Promise.reject(error);
            }
        );

        // Cleanup: remover el interceptor cuando el componente se desmonte
        return () => {
            axios.interceptors.response.eject(responseInterceptor);
        };
    }, []); // Solo se ejecuta una vez al montar el componente

    useEffect(() => {
        const loadAuth = async () => {
            try {
                // const token = await SecureStore.getItemAsync(TOKEN_KEY);
                // const userRaw = await SecureStore.getItemAsync(USER_KEY);
                const email = await SecureStore.getItemAsync(EMAIL_KEY);
                const password = await SecureStore.getItemAsync(PASSWORD_KEY);
                const biometrics = await SecureStore.getItemAsync(BIOMETRICS_KEY);
                const pastUser = await SecureStore.getItemAsync(PAST_USER_KEY);
                const pastToken = await SecureStore.getItemAsync(PAST_DEVICE_TOKEN_KEY);

                console.log('🔐 Cargando sesión almacenada:', { email, password, biometrics, pastUser});


                // if (token && userRaw) {
                //
                //     const decoded = jwtDecode<JwtPayload>(token);
                //     const currentTime = Date.now() / 1000;
                //
                //     if (decoded.exp < currentTime) {
                //         console.warn('⚠️ Token expirado. Cerrando sesión automáticamente.');
                //         await logout();
                //         return;
                //     }
                //
                //     const user = JSON.parse(userRaw);
                //     axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
                //     setAuthState({
                //         token,
                //         user,
                //         authenticated: true,
                //         loading: false,
                //         biometrics: biometrics,
                //         pastUser: pastUser
                //     });
                // } else {
                //     setAuthState({token: null, user: null, authenticated: false, loading: false, biometrics: biometrics, pastUser: pastUser});
                // }

                await logout();

                setAuthState({
                    token: null,
                    user: null,
                    authenticated: false,
                    loading: false,
                    biometrics: biometrics,
                    pastUser: pastUser,
                    pastDeviceToken: pastToken
                });
            } catch (error) {
                console.error('❌ Error al cargar sesión:', error);
                setAuthState({token: null, user: null, authenticated: false, loading: false, biometrics: "false", pastUser: null, pastDeviceToken: null});
            }
        };

        loadAuth();
    }, []);

    const loginForFinger = async (email: string, password: string) => {
        try {
            const {data} = await axios.post(`${API_URL}/login`, {email, password});

            const token = data.token;

            if (token) {
                await SecureStore.setItemAsync(EMAIL_KEY, String(email));
                await SecureStore.setItemAsync(PASSWORD_KEY, String(password));
                await SecureStore.setItemAsync(BIOMETRICS_KEY, "true");
            }

            return {successModal: true};
        } catch (error: any) {
            return {
                error: true,
                msg: error.response?.data?.error?.message || error.message,
            };
        }
    };

    const login = async (email: string, password: string) => {
        console.log(`${API_URL}/login`, { email, password })
        try {
            const {data} = await axios.post(`${API_URL}/login`, {email, password});

            const token = data.token;

            await SecureStore.setItemAsync('user_id', String(data.user_id));
            await SecureStore.setItemAsync('company_id', String(data.company_id));
            const pastUser = await SecureStore.getItemAsync(PAST_USER_KEY);

            if (!token) {
                console.warn('❌ No se recibió el token');
                return {error: true, msg: 'Credenciales incorrectas o token faltante'};
            }

            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

            const profileResponse = await getProfile();
            if (profileResponse.error) {
                return {error: true, msg: 'No se pudo obtener el perfil del usuario.'};
            }

            const userProfile = {
                ...profileResponse.profile,
                id: profileResponse.profile.user_id,
                company_name: (profileResponse.profile as any).company_name || 'N/A', // Agregar company_name
                photo: profileResponse.profile.photo as any, // Cast para evitar error de tipo
            };

            await SecureStore.setItemAsync(TOKEN_KEY, token);
            await SecureStore.setItemAsync(USER_KEY, JSON.stringify(userProfile));


            setAuthState({
                token,
                user: userProfile as any, // Cast para evitar problemas de tipos
                authenticated: true,
                loading: false,
                biometrics: authState.biometrics,
                pastUser: pastUser,
                pastDeviceToken: authState.pastDeviceToken
            });

            return {token, user: userProfile};
        } catch (error: any) {
            console.error('❌ Error en login:', error.response?.data || error.message);

            // Extraer el código de error específico del servidor
            const errorCode = error.response?.data?.error?.code || 'AUTH_LOGIN_FAILED';
            const errorMessage = error.response?.data?.error?.message || error.response?.data?.message || 'Error al iniciar sesión';

            return {
                error: true,
                msg: errorMessage,
                code: errorCode,
            };
        }
    };

    const register = async (payload: RegisterPayload) => {

        return await registerUser(payload);
    };




    return (
        <AuthContext.Provider value={{authState, setAuthState, login, logout, register, loginForFinger}}>
            {children}
        </AuthContext.Provider>
    );
};
