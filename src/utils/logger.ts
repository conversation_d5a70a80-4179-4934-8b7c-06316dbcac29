// src/utils/logger.ts

/**
 * Logger utility para manejar logs de manera condicional
 * En producción, solo muestra errores críticos
 */

const IS_DEVELOPMENT = __DEV__;

export const logger = {
  /**
   * Log de desarrollo - solo se muestra en desarrollo
   */
  dev: (...args: any[]) => {
    if (IS_DEVELOPMENT) {
      console.log(...args);
    }
  },

  /**
   * Log de información - solo se muestra en desarrollo
   */
  info: (...args: any[]) => {
    if (IS_DEVELOPMENT) {
      console.info(...args);
    }
  },

  /**
   * Log de advertencia - se muestra siempre
   */
  warn: (...args: any[]) => {
    console.warn(...args);
  },

  /**
   * Log de error - se muestra siempre
   */
  error: (...args: any[]) => {
    console.error(...args);
  },

  /**
   * Log crítico - se muestra siempre y podría enviarse a analytics
   */
  critical: (...args: any[]) => {
    console.error('🚨 CRITICAL:', ...args);
    // Aquí se podría agregar envío a analytics/crashlytics
  },

  /**
   * Log de éxito - solo en desarrollo
   */
  success: (...args: any[]) => {
    if (IS_DEVELOPMENT) {
      console.log('✅', ...args);
    }
  },

  /**
   * Log de debug con emoji - solo en desarrollo
   */
  debug: (emoji: string, ...args: any[]) => {
    if (IS_DEVELOPMENT) {
      console.log(emoji, ...args);
    }
  }
};

export default logger;
