// src/utils/alert.utils.ts
import { Alert } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { Vibration } from 'react-native';

/**
 * 🔍 Mostrar un diálogo de confirmación bonito con opciones Sí/No
 */
export function showConfirmationDialog(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
) {
    Vibration.vibrate(50);

    showMessage({
        message: title,
        description: `${message}\n\n✅ Toca para confirmar`,
        type: 'info',
        icon: 'auto',
        duration: 6000, // Más tiempo para que el usuario pueda leer y decidir
        floating: true,
        position: 'top',
        onPress: () => {
            console.log('✅ Usuario confirmó acción');
            onConfirm();
        },
        onHide: () => {
            // Si la alerta se cierra sin presionar, ejecutar onCancel
            if (onCancel) {
                console.log('❌ Usuario canceló acción (timeout)');
                onCancel();
            }
        }
    });
}

/**
 * 📸 Configuración para modal de confirmación de foto
 * Esta función retorna la configuración que debe usarse con ConfirmationModal
 */
export function getPhotoConfirmationConfig(isForBeneficiary: boolean = false) {
    return {
        title: isForBeneficiary ? '¿Actualizar foto del beneficiario?' : '¿Actualizar foto de perfil?',
        message: isForBeneficiary
            ? 'La foto del beneficiario se actualizará con la imagen seleccionada.'
            : 'Tu foto de perfil se actualizará con la imagen seleccionada.',
        icon: '📸',
        confirmText: 'Actualizar',
        cancelText: 'Cancelar'
    };
}

export function showApiErrorAlert(message?: string) {
    Alert.alert(
        '⚠️ Algo salió mal',
        message || 'No se pudo completar la acción. Intenta más tarde.'
    );
}

export function showErrorAlert(apiError: any) {
    const code = apiError?.error?.code || 'GENERIC';
    const message = apiError?.error?.message || apiError?.error?.message?.msg || 'Ocurrió un error inesperado.';

    let description = '';
    let title = '😕 Ocurrió un error';

    switch (code) {
        // Errores de registro - Códigos AC
        case 'AC-001':
            title = '🆔 CURP no válida';
            description = 'El CURP ingresado no existe en nuestro sistema o ya está registrado. Por favor verifica los datos.';
            break;
        case 'AC-006':
            title = '📧 Email ya registrado';
            description = 'Este correo electrónico ya está registrado. ¿Deseas iniciar sesión?';
            break;
        case 'AC-007':
            title = '🔧 Error de registro';
            description = 'Error temporal durante el registro. Por favor intenta nuevamente.';
            break;
        case 'AUTH_SMS_SENDING_FAILED':
            title = '📱 Error enviando SMS';
            description = 'No se pudo enviar el código de verificación. ¿Deseas intentar nuevamente?';
            break;

        // Errores de validación - Códigos RVS
        case 'RVS-001':
            title = '📝 Formato de datos inválido';
            description = 'El formato de los datos no es válido. Por favor intenta nuevamente.';
            break;
        case 'RVS-002':
            title = '⚠️ Errores de validación';
            description = 'Uno o más campos no cumplen con las validaciones requeridas.';
            break;

        // Errores legacy (mantener compatibilidad)
        case 'AC-002':
            title = '🆔 CURP no válida';
            description = 'La CURP no coincide con ningún registro en la empresa.';
            break;
        case 'AC-003':
            title = '📱 Error de verificación';
            description = 'Falló la verificación del teléfono. Intenta de nuevo.';
            break;
        case 'AC-004':
            title = '🏢 Empresa inactiva';
            description = 'La empresa no está activa en el sistema.';
            break;
        case 'AC-005':
            title = '✅ Ya verificado';
            description = 'Tu cuenta ya está verificada. Inicia sesión directamente.';
            break;

        // Errores de validación de campos
        case 'VALIDATION_MISSING_FIELDS':
            title = '📝 Campos requeridos';
            description = 'Por favor completa todos los campos obligatorios.';
            break;
        case 'VALIDATION_INVALID_EMAIL':
            title = '📧 Email inválido';
            description = 'Por favor ingresa un correo electrónico válido.';
            break;
        case 'VALIDATION_INVALID_PHONE':
            title = '📱 Teléfono inválido';
            description = 'Por favor ingresa un número de teléfono válido (10 dígitos).';
            break;
        case 'VALIDATION_INVALID_CURP':
            title = '🆔 CURP inválida';
            description = 'Por favor ingresa una CURP válida (18 caracteres).';
            break;
        case 'VALIDATION_PASSWORD_MISMATCH':
            title = '🔐 Contraseñas no coinciden';
            description = 'Las contraseñas ingresadas no son iguales.';
            break;
        case 'VALIDATION_WEAK_PASSWORD':
            title = '🔐 Contraseña débil';
            description = 'La contraseña debe tener al menos 8 caracteres.';
            break;

        // Errores específicos de verificación telefónica (PVC-XXX)
        case 'PVC-001':
            title = '👤 Usuario no encontrado';
            description = 'El usuario no existe o no está activo. Por favor regístrate nuevamente.';
            break;
        case 'PVC-002':
            title = '✅ Ya verificado';
            description = 'Tu cuenta ya está verificada. Puedes continuar.';
            break;
        case 'PVC-003':
            title = '📱 Sin código de verificación';
            description = 'No tienes un código de verificación. ¿Deseas solicitar uno nuevo?';
            break;
        case 'PVC-004':
            title = '🔢 Código incorrecto';
            description = 'El código de verificación es incorrecto. Verifica e intenta de nuevo.';
            break;
        case 'PVC-005':
            title = '📝 Error de validación';
            description = 'Por favor ingresa un código de verificación válido de 6 dígitos.';
            break;
        case 'PVC-006':
            title = '🔧 Error de base de datos';
            description = 'Error en la base de datos durante la verificación. Intenta de nuevo en unos momentos.';
            break;
        case 'PVC-007':
            title = '🔧 Error interno del servidor';
            description = 'Error interno del servidor durante la verificación. Intenta de nuevo en unos momentos.';
            break;
        case 'PVC-008':
            title = '🔧 Error de procesamiento';
            description = 'Error al procesar la solicitud de verificación. Intenta de nuevo en unos momentos.';
            break;
        case 'PVC-009':
            title = '📝 Formato de teléfono inválido';
            description = 'El formato del número de teléfono no es válido.';
            break;
        case 'PVC-010':
            title = '📱 Teléfono requerido';
            description = 'El número de teléfono es obligatorio.';
            break;
        case 'PVC-011':
            title = '📱 Teléfono no encontrado';
            description = 'No se encontró un usuario con este número de teléfono.';
            break;
        case 'PVC-012':
            title = '🔧 Error enviando SMS';
            description = 'Error al enviar el código de verificación por SMS.';
            break;
        case 'PVC-013':
            title = '🔧 Error de base de datos';
            description = 'Error en la base de datos durante la verificación del teléfono.';
            break;
        case 'PVC-014':
            title = '🔧 Error interno del servidor';
            description = 'Error interno del servidor durante la verificación del teléfono.';
            break;
        case 'PVC-015':
            title = '🔧 Error de procesamiento';
            description = 'Error al procesar la solicitud de verificación del teléfono.';
            break;

        // Errores genéricos de verificación (fallback)
        case 'VERIFICATION_CODE_INVALID':
            title = '🔢 Código incorrecto';
            description = 'El código ingresado no es válido. Verifica e intenta de nuevo.';
            break;
        case 'VERIFICATION_CODE_EXPIRED':
            title = '⏰ Código expirado';
            description = 'El código ha expirado. Solicita un nuevo código.';
            break;
        case 'VERIFICATION_CODE_EMPTY':
            title = '📝 Código requerido';
            description = 'Por favor ingresa el código de verificación.';
            break;
        case 'VERIFICATION_ALREADY_VERIFIED':
            title = '✅ Ya verificado';
            description = 'Tu número ya está verificado. Puedes continuar.';
            break;

        // Errores de autenticación
        case 'AUTH_USER_INCORRECT_PASSWORD':
            title = '🔐 Credenciales incorrectas';
            description = 'La contraseña que ingresaste no es correcta. Verifica e intenta de nuevo.';
            break;
        case 'AUTH_USER_NOT_FOUND':
            title = '👤 Usuario no encontrado';
            description = 'No existe una cuenta con este correo electrónico.';
            break;
        case 'AUTH_LOGIN_FAILED':
            title = '🚫 Error de autenticación';
            description = message;
            break;
        case 'AUTH_BIOMETRIC_LOGIN_FAILED':
            title = '👆 Error de autenticación biométrica';
            description = message;
            break;
        case 'AUTH_ACCOUNT_NOT_VERIFIED':
            title = '📱 Cuenta no verificada';
            description = 'Debes verificar tu número de teléfono antes de iniciar sesión.';
            break;
        case 'SESSION_EXPIRED':
            title = '⏰ Sesión expirada';
            description = 'Tu sesión ha expirado por seguridad. Por favor inicia sesión nuevamente.';
            break;

        // Errores del servidor
        case 'SERVER_ERROR_500':
            title = '🔧 Error del servidor';
            description = 'Ocurrió un problema en nuestros servidores. Intenta de nuevo en unos momentos.';
            break;
        case 'SERVER_ERROR_TIMEOUT':
            title = '⏰ Tiempo agotado';
            description = 'La solicitud está tardando demasiado. Verifica tu conexión e intenta de nuevo.';
            break;
        case 'NETWORK_ERROR':
            title = '🌐 Sin conexión';
            description = 'Verifica tu conexión a internet e intenta de nuevo.';
            break;

        default:
            description = message;
    }

    showMessage({
        message: title,
        description,
        type: 'danger',
        icon: 'auto',
        duration: 4000,
        floating: true,
        position: 'top',
    });
}

/**
 * ✅ Mostrar un mensaje bonito de éxito
 */
export function showSuccessAlert(message: string, description?: string, onPress?: () => void) {
    Vibration.vibrate(100);

    showMessage({
        message,
        description: description || '',
        type: 'success',
        icon: 'auto',
        duration: onPress ? 3000 : 4000, // Reducido en 1 segundo
        floating: true,
        position: 'top',
        onPress: onPress,
        onHide: onPress, // Ejecutar callback cuando la alerta se cierre automáticamente
    });
}

/**
 * ℹ️ Mostrar una alerta cuando no se detectaron cambios
 */
export function showNoChangesAlert({message, description}) {
    Vibration.vibrate(50);

    showMessage({
        message: message,
        description: description,
        type: 'info',
        icon: 'info',
        duration: 3000,
        floating: true,
        position: 'top',
    });
}

/**
 * ⚠️ Mostrar alerta de validación específica
 */
export function showValidationAlert(field: string, message: string) {
    const fieldNames = {
        'workerId': 'Número de empleado',
        'curp': 'CURP',
        'phone': 'Teléfono',
        'email': 'Correo electrónico',
        'password': 'Contraseña',
        'confirmPassword': 'Confirmación de contraseña',
        'code': 'Código de verificación'
    };

    const fieldName = fieldNames[field] || field;

    showMessage({
        message: `⚠️ ${fieldName}`,
        description: message,
        type: 'warning',
        icon: 'auto',
        duration: 4500, // Más tiempo para leer
        floating: true,
        position: 'top',
    });
}

/**
 * 🔧 Mostrar alerta específica para errores del servidor
 */
export function showServerErrorAlert(statusCode?: number, customMessage?: string) {
    let title = '🔧 Error del servidor';
    let description = customMessage || 'Ocurrió un problema en nuestros servidores. Intenta de nuevo en unos momentos.';

    if (statusCode === 500) {
        title = '🔧 Error interno del servidor';
        description = customMessage || 'Nuestros servidores están experimentando problemas. Intenta de nuevo en unos momentos.';
    } else if (statusCode === 503) {
        title = '🚧 Servicio no disponible';
        description = 'El servicio está temporalmente no disponible. Intenta más tarde.';
    } else if (statusCode === 408) {
        title = '⏰ Tiempo agotado';
        description = 'La solicitud tardó demasiado tiempo. Verifica tu conexión e intenta de nuevo.';
    }

    Vibration.vibrate([100, 50, 100]);

    showMessage({
        message: title,
        description,
        type: 'danger',
        icon: 'auto',
        duration: 6000, // Más tiempo para leer errores del servidor
        floating: true,
        position: 'top',
    });
}

/**
 * 📱 Mostrar alerta específica para errores de verificación de código
 */
export function showVerificationErrorAlert(errorType: 'invalid' | 'expired' | 'server' | 'empty', customMessage?: string) {
    let title = '🔢 Error de verificación';
    let description = customMessage || 'Ocurrió un error con el código de verificación.';

    switch (errorType) {
        case 'invalid':
            title = '🔢 Código incorrecto';
            description = customMessage || 'El código ingresado no es válido. Verifica e intenta de nuevo.';
            break;
        case 'expired':
            title = '⏰ Código expirado';
            description = customMessage || 'El código ha expirado. Solicita un nuevo código.';
            break;
        case 'server':
            title = '🔧 Error del servidor';
            description = customMessage || 'Ocurrió un problema al verificar el código. Intenta de nuevo.';
            break;
        case 'empty':
            title = '📝 Código requerido';
            description = customMessage || 'Por favor ingresa el código de verificación.';
            break;
    }

    Vibration.vibrate(100);

    showMessage({
        message: title,
        description,
        type: 'danger',
        icon: 'auto',
        duration: 5000, // Más tiempo para leer errores de verificación
        floating: true,
        position: 'top',
    });
}

/**
 * 🔄 Mostrar alerta con opción de reintentar
 */
export function showRetryAlert(message: string, description: string, onRetry?: () => void) {
    showMessage({
        message: `🔄 ${message}`,
        description: onRetry ? `${description}\n\nToca para reintentar.` : description,
        type: 'warning',
        icon: 'auto',
        duration: onRetry ? 6000 : 4000,
        floating: true,
        position: 'top',
        onPress: onRetry,
    });
}

/**
 * 📱 Mostrar alerta específica para códigos de error PVC (Phone Verification Code)
 */
export function showPVCErrorAlert(
    errorCode: string,
    message: string,
    onResendCode?: () => void,
    onNavigateToRegister?: () => void,
    onNavigateToMain?: () => void
) {
    let title = '📱 Error de verificación';
    let description = message;
    let type: 'danger' | 'warning' | 'success' = 'danger';
    let duration = 5000; // Más tiempo para leer
    let onPress: (() => void) | undefined;

    switch (errorCode) {
        case 'PVC-001':
            title = '👤 Usuario no encontrado';
            description = 'El usuario no existe o no está activo.';
            onPress = onNavigateToRegister;
            break;
        case 'PVC-002':
            title = '✅ Ya verificado';
            description = 'Tu cuenta ya está verificada.';
            type = 'success';
            onPress = onNavigateToMain;
            break;
        case 'PVC-003':
            title = '📱 Sin código de verificación';
            description = 'No tienes un código de verificación. ¿Deseas solicitar uno nuevo?';
            type = 'warning';
            onPress = onResendCode;
            duration = 7000; // Más tiempo para casos que requieren acción
            break;
        case 'PVC-004':
            title = '🔢 Código incorrecto';
            description = 'El código de verificación es incorrecto. Verifica e intenta de nuevo.';
            break;
        case 'PVC-005':
            title = '📝 Error de validación';
            description = 'Por favor ingresa un código de verificación válido de 6 dígitos.';
            break;
        case 'PVC-006':
        case 'PVC-007':
        case 'PVC-008':
        case 'PVC-012':
        case 'PVC-013':
        case 'PVC-014':
        case 'PVC-015':
            title = '🔧 Error del servidor';
            description = 'Error temporal del servidor. Intenta de nuevo en unos momentos.';
            type = 'warning';
            duration = 6000; // Más tiempo para errores del servidor
            break;
        case 'PVC-009':
            title = '📝 Formato inválido';
            description = 'El formato del número de teléfono no es válido.';
            break;
        case 'PVC-010':
            title = '📱 Teléfono requerido';
            description = 'El número de teléfono es obligatorio.';
            break;
        case 'PVC-011':
            title = '📱 Teléfono no encontrado';
            description = 'No se encontró un usuario con este número de teléfono.';
            break;
        default:
            title = '❌ Error inesperado';
            description = message || 'Ha ocurrido un error inesperado. Por favor intenta nuevamente.';
    }

    Vibration.vibrate(type === 'success' ? 100 : [100, 50, 100]);

    showMessage({
        message: title,
        description: onPress && (errorCode === 'PVC-003' || errorCode === 'PVC-001' || errorCode === 'PVC-002')
            ? `${description}\n\nToca para continuar.`
            : description,
        type,
        icon: 'auto',
        duration,
        floating: true,
        position: 'top',
        onPress,
    });
}

/**
 * 📝 Mostrar alerta específica para errores de registro
 */
export function showRegistrationErrorAlert(
    errorCode: string,
    message: string,
    validationErrors?: string[],
    onNavigateToLogin?: () => void,
    onRetry?: () => void
) {
    let title = '📝 Error de registro';
    let description = message;
    let type: 'danger' | 'warning' | 'info' = 'danger';
    let duration = 4000;
    let onPress: (() => void) | undefined;

    switch (errorCode) {
        case 'RVS-001':
            title = '📝 Formato de datos inválido';
            description = 'Error en el formato de datos. Por favor intenta nuevamente.';
            break;
        case 'RVS-002':
            title = '⚠️ Errores de validación';
            if (validationErrors && validationErrors.length > 0) {
                description = validationErrors[0]; // Mostrar el primer error
            } else {
                description = 'Uno o más campos no cumplen con las validaciones requeridas.';
            }
            type = 'warning';
            break;
        case 'AC-001':
            title = 'CURP no válido';
            description = 'El CURP ingresado no existe en nuestro sistema o ya está registrado. Por favor verifica los datos.';
            break;
        case 'AC-006':
            title = '📧 Email ya registrado';
            description = 'Este correo electrónico ya está registrado. ¿Deseas iniciar sesión?';
            type = 'warning';
            onPress = onNavigateToLogin;
            duration = 6000;
            break;
        case 'AC-007':
            title = '🔧 Error de registro';
            description = 'Error temporal durante el registro. Por favor intenta nuevamente.';
            onPress = onRetry;
            duration = 5000;
            break;
        case 'AUTH_SMS_SENDING_FAILED':
            title = '📱 Error enviando SMS';
            description = 'No se pudo enviar el código de verificación. ¿Deseas intentar nuevamente?';
            type = 'warning';
            onPress = onRetry;
            duration = 6000;
            break;
        default:
            title = '❌ Error inesperado';
            description = message || 'Ha ocurrido un error inesperado durante el registro. Por favor intenta nuevamente.';
    }

    Vibration.vibrate(type === 'danger' ? [100, 50, 100] : 100);

    showMessage({
        message: title,
        description: onPress && (errorCode === 'AC-006' || errorCode === 'AC-007' || errorCode === 'AUTH_SMS_SENDING_FAILED')
            ? `${description}\n\nToca para continuar.`
            : description,
        type,
        icon: 'auto',
        duration,
        floating: true,
        position: 'top',
        onPress,
    });
}

/**
 * 📋 Procesar y mostrar errores de validación específicos por campo
 */
export function showFieldValidationErrors(validationErrors: string[]) {
    // Crear objeto para mapear errores por campo
    const fieldErrors: { [key: string]: string } = {};

    validationErrors.forEach(errorMessage => {
        if (errorMessage.includes('CURP')) {
            fieldErrors.curp = errorMessage;
        } else if (errorMessage.includes('correo') || errorMessage.includes('email')) {
            fieldErrors.email = errorMessage;
        } else if (errorMessage.includes('contraseña')) {
            fieldErrors.password = errorMessage;
        } else if (errorMessage.includes('empleado')) {
            fieldErrors.workerId = errorMessage;
        } else if (errorMessage.includes('teléfono')) {
            fieldErrors.phone = errorMessage;
        }
    });

    // Mostrar el primer error encontrado
    const firstError = validationErrors[0];
    const firstField = Object.keys(fieldErrors)[0] || 'general';

    showValidationAlert(firstField, firstError);

    return fieldErrors;
}
