import { StyleSheet } from 'react-native';
import { vs, s, ms } from 'react-native-size-matters';

export const errorLogsStyles = StyleSheet.create({
    container: {
        flex: 1,
        padding: s(15),
    },
    header: {
        fontSize: ms(18, 0.3),
        fontWeight: 'bold',
        color: '#FFF',
        marginBottom: vs(15),
        textAlign: 'center',
    },
    filesList: {
        marginBottom: vs(15),
        maxHeight: vs(150),
        borderRadius: s(5),
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    fileItem: {
        padding: s(10),
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(255, 255, 255, 0.1)',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    fileItemSelected: {
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
    },
    fileItemText: {
        color: '#FFF',
        fontSize: ms(14, 0.3),
    },
    fileItemInfo: {
        color: '#CCC',
        fontSize: ms(12, 0.3),
    },
    contentContainer: {
        flex: 1,
        marginBottom: vs(15),
        borderRadius: s(5),
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    logContent: {
        padding: s(10),
        maxHeight: vs(250),
    },
    logLine: {
        color: '#FFF',
        fontSize: ms(12, 0.3),
        fontFamily: 'monospace',
        marginBottom: vs(2),
    },
    logLineError: {
        color: '#FF5252',
    },
    logLineWarning: {
        color: '#FFD740',
    },
    logLineInfo: {
        color: '#40C4FF',
    },
    logLineDebug: {
        color: '#69F0AE',
    },
    logLineNotice: {
        color: '#B388FF',
    },
    filterContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: vs(10),
        flexWrap: 'wrap',
    },
    filterButton: {
        paddingVertical: vs(5),
        paddingHorizontal: s(10),
        borderRadius: s(15),
        marginRight: s(5),
        marginBottom: vs(5),
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    filterButtonActive: {
        backgroundColor: '#385927',
    },
    filterButtonText: {
        color: '#FFF',
        fontSize: ms(12, 0.3),
    },
    searchContainer: {
        flexDirection: 'row',
        marginBottom: vs(10),
    },
    searchInput: {
        flex: 1,
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: s(5),
        padding: s(8),
        color: '#FFF',
        marginRight: s(10),
    },
    searchButton: {
        backgroundColor: '#385927',
        borderRadius: s(5),
        padding: s(8),
        justifyContent: 'center',
        alignItems: 'center',
    },
    searchButtonText: {
        color: '#FFF',
        fontSize: ms(14, 0.3),
    },
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: vs(10),
    },
    paginationButton: {
        backgroundColor: '#385927',
        borderRadius: s(5),
        padding: s(8),
        minWidth: s(80),
        justifyContent: 'center',
        alignItems: 'center',
    },
    paginationButtonDisabled: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
    },
    paginationButtonText: {
        color: '#FFF',
        fontSize: ms(14, 0.3),
    },
    paginationInfo: {
        color: '#FFF',
        fontSize: ms(14, 0.3),
        alignSelf: 'center',
    },
    latestErrorsContainer: {
        marginTop: vs(15),
    },
    latestErrorsHeader: {
        fontSize: ms(16, 0.3),
        fontWeight: 'bold',
        color: '#FFF',
        marginBottom: vs(10),
    },
    latestErrorItem: {
        padding: s(10),
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(255, 255, 255, 0.1)',
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
        borderRadius: s(5),
        marginBottom: vs(5),
    },
    latestErrorFile: {
        color: '#FFF',
        fontSize: ms(14, 0.3),
        fontWeight: 'bold',
        marginBottom: vs(5),
    },
    latestErrorContent: {
        color: '#FF5252',
        fontSize: ms(12, 0.3),
        fontFamily: 'monospace',
    },
    emptyText: {
        color: '#FFF',
        textAlign: 'center',
        marginVertical: vs(20),
    },
    regresar: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: vs(20),
    },
    regresarText: {
        color: '#FFF',
        marginLeft: s(5),
        fontSize: ms(16, 0.3),
    },
});