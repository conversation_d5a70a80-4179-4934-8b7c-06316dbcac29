// src/styles/changePasswordStyle.ts
import { StyleSheet } from 'react-native';
import {scale, vs, moderateScale, ms} from 'react-native-size-matters';
import {borderRadius, globalTheme} from "../constants/theme";


const styles = StyleSheet.create({
    card: {
        backgroundColor: globalTheme.container_translucent,
        borderRadius: borderRadius,
        padding: moderateScale(25),
        width: '80%',
        minHeight: '70%',
        alignSelf: 'center',
        marginTop: vs(100),
    },
    title: {
        color: globalTheme.text_head,
        fontSize: vs(18),
        fontWeight: 'bold',
        marginBottom: vs(20),
        alignSelf: 'center',
        textAlign: "center"
    },
    label: {
        color: globalTheme.text_head,
        fontSize: vs(14),
        marginBottom: vs(12),
        marginTop: vs(8),
        textAlign: "center",
        fontWeight: '500',
    },
    inputBox: {
        position: 'relative',
        justifyContent: "center",
        marginBottom: vs(20),
    },
    input: {
        backgroundColor: globalTheme.input,
        borderRadius: borderRadius,
        paddingHorizontal: vs(15),
        paddingVertical: vs(6),
        color: globalTheme.text_contrast,
        width: '100%',
        fontSize: vs(16),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        borderWidth: 1,
        borderColor: '#E5E7EB',
    },
    eyeIcon: {
        position: 'absolute',
        right: vs(20),
        top: '50%',
        transform: [{ translateY: -12 }],
        padding: vs(4),
    },
    btn: {
        backgroundColor: globalTheme.button_ok,
        borderRadius: borderRadius,
        paddingVertical: vs(6),
        marginTop: vs(20),
        alignItems: 'center',
        width: '60%',
        alignSelf: "center"
    },
    btnText: {
        color: globalTheme.text_head,
        fontWeight: 'bold',
        fontSize: vs(14),
    },
    errorText: {
        color: globalTheme.text_error,
        fontSize: moderateScale(12),
        marginBottom: vs(15),
        marginTop: vs(5),
        textAlign: 'center',
        backgroundColor: 'rgba(255, 107, 107, 0.1)',
        paddingVertical: vs(8),
        paddingHorizontal: vs(12),
        borderRadius: vs(8),
        borderWidth: 1,
        borderColor: 'rgba(255, 107, 107, 0.3)',
    },
    link1: {
        color: '#ccc',
        fontSize: vs(11),
        textAlign: 'center',
        textDecorationLine: 'underline',
        textTransform: "uppercase",
    },
    iconOverlay: {
        position: 'absolute',
        right: vs(10),
        zIndex: 1,
    },
    inputContainer: {
        justifyContent: 'center',
        width: '100%',
        marginBottom: vs(20)
    },

});

export default styles;
