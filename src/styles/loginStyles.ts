import { StyleSheet } from 'react-native';
import {vs} from 'react-native-size-matters';
import {borderRadius, globalTheme} from "../constants/theme";




export const loginStyles = StyleSheet.create({
    scrollContainer: {
        flexGrow: 1,
        paddingTop: vs(50),
        paddingBottom: vs(20),
        alignItems: 'center',
        justifyContent: 'center',
    },
    wrapper: {
        flex: 1,
        position: 'relative',
    },
    title: {
        color: globalTheme.text_head,
        fontSize: vs(20),
        fontWeight: 'semibold',
        textAlign: 'center',
        marginBottom: vs(45),
    },
    label: {
        color: globalTheme.text_head,
        alignContent: 'center',
        marginBottom: vs(10),
        fontSize: vs(14)
    },
    inputContainer: {
        justifyContent: 'center',
        width: '100%',
    },
    input: {
        backgroundColor: globalTheme.input,
        borderRadius: borderRadius,
        paddingHorizontal: vs(15),
        paddingVertical: vs(6),
        color: globalTheme.text_contrast,
        width: '100%',
    },
    iconOverlay: {
        position: 'absolute',
        right: vs(10),
        zIndex: 1,
    },
    loginBtn: {
        backgroundColor: globalTheme.button_ok,
        borderRadius: borderRadius,
        paddingVertical: vs(8),
        alignItems: 'center',
        width: vs(145),
        marginVertical: vs(30)
    },
    loginText: {
        color: globalTheme.text_head,
        fontWeight: 'bold',
    },
    link: {
        color: globalTheme.text_head,
        fontSize: vs(11),
        textAlign: 'center',
        textDecorationLine: 'underline',
        fontWeight: "bold"
    },
    link1: {
        color: globalTheme.text_light,
        fontSize: vs(11),
        textAlign: 'center',
        textDecorationLine: 'underline',
        textTransform: "uppercase"
    },
    registerText: {
        color: globalTheme.text_light,
        fontSize: vs(11),
        textAlign: 'center',
    },
    registerLink: {
        color: globalTheme.text_special,
        fontWeight: 'bold',
        textDecorationLine: 'underline',
        fontSize: vs(11),
    },
    errorText: {
        color: globalTheme.text_error,
        fontSize: 12,
        marginBottom: vs(5),
    },
    buttonText: {
        color: globalTheme.text_head,
        fontWeight: '900',
        fontSize: vs(10),
        flexWrap: "wrap",
        textAlign: "center"
    },
    form: {
        alignItems: "center",
        backgroundColor: globalTheme.container_translucent,
        borderRadius: borderRadius,
        width: '80%',
        minHeight: '70%',
        flexShrink: 1,
        flexGrow: 0,
        marginTop: vs(50),
        paddingHorizontal: vs(25),
        paddingVertical: vs(15),
        marginBottom: vs(20)
    }

});
