// src/styles/forgotPasswordStyles.ts
import { StyleSheet } from 'react-native';
import {mvs, ms, vs} from 'react-native-size-matters';
import {borderRadius, globalTheme} from "../constants/theme";

export const forgotPasswordStyles = StyleSheet.create({
    wrapper: {
        flex: 1,
        alignItems: "center",
        paddingTop: vs(50)
    },
    container: {
        alignItems: "center",
        backgroundColor: globalTheme.container_translucent,
        borderRadius: borderRadius,
        width: '80%',
        height: '75%',
        marginTop: vs(60),
        paddingHorizontal: vs(25),
        paddingVertical: vs(15)
    },
    content: {
        width: '100%',
        alignItems: 'center',
        paddingTop: vs(10)
    },
    input: {
        width: '100%',
        backgroundColor: globalTheme.input,
        borderRadius: borderRadius,
        paddingHorizontal: ms(16),
        fontSize: ms(14),
        marginBottom: mvs(25),
        color: globalTheme.text_contrast,
        height: vs(40)
    },
    button: {
        backgroundColor: globalTheme.button_ok,
        borderRadius: borderRadius,
        paddingVertical: vs(8),
        alignItems: 'center',
        width: vs(145),
        marginBottom: vs(30)
    },
    buttonDisabled: {
        opacity: 0.7
    },
    buttonText: {
        color: globalTheme.text_head,
        fontWeight: '900',
        fontSize: vs(10),
        flexWrap: "wrap",
        textAlign: "center"
    },
    label: {
        color: globalTheme.text_head,
        alignContent: 'center',
        marginBottom: vs(10)
    },
    description: {
        color: globalTheme.text_head,
        fontSize: vs(12),
        textAlign: 'center',
        marginBottom: vs(20),
        marginTop: vs(10)
    },
    highlight: {
        fontWeight: 'bold',
        color: '#ffffff'
    },
    link1: {
        color: '#ccc',
        fontSize: vs(11),
        textAlign: 'center',
        textDecorationLine: 'underline',
        textTransform: "uppercase"
    },
    inputContainer: {
        position: 'relative',
        justifyContent: 'center',
        width: '100%',
        marginBottom: vs(20)
    }
});
