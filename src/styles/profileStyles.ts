// src/styles/profileStyle.ts
import {StyleSheet} from 'react-native';
import {
    scale,
    verticalScale as vs,
    moderateScale as ms,
    moderateVerticalScale as mvs,
} from 'react-native-size-matters';
import {borderRadius, globalTheme} from "../constants/theme";


export const profileStyle = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: globalTheme.gradient[1],
    },
    logo: {
        width: scale(40),
        height: scale(40),
        resizeMode: 'contain',
    },
    content: {
        padding: scale(20),
    },
    profileBox: {
        backgroundColor: 'rgba(255,255,255,0.34)',
        borderRadius: borderRadius,
        paddingTop: vs(70),
        paddingBottom: vs(30),
        alignItems: 'center',
        paddingHorizontal: scale(25),
        width: '85%',
        alignSelf: 'center',
        marginVertical: vs(20)
    },
    cameraButton: {
        backgroundColor: '#00aaff',
        borderRadius: ms(30),
        padding: ms(10),
        marginBottom: mvs(5),
    },
    cameraText: {
        color: '#fff',
        marginBottom: mvs(15),
        fontSize: ms(12),
    },
    fieldContainer: {
        width: '100%',
        marginBottom: mvs(10),
        justifyContent: "center",
        alignItems: "center"
    },
    label: {
        color: globalTheme.text_head,
        fontSize: ms(16),
        marginBottom: mvs(5),
        textTransform: "uppercase",
        fontWeight: "600",
    },
    inputBox: {
        width: '100%',
        borderColor: globalTheme.container_light,
        borderWidth: 0.5,
        borderRadius: ms(20),
        paddingVertical: mvs(8),
        paddingHorizontal: ms(15),
    },
    inputText: {
        color: globalTheme.text_head,
        fontSize: ms(16),
        paddingVertical: vs(1),
        fontWeight: "500"
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginTop: mvs(20),
        gap: scale(8),
    },
    largeButton: {
        borderRadius: borderRadius,
        padding: ms(12),
        alignItems: 'center',
        width: '100%',
        marginTop: mvs(10),
        flexDirection: "row",
        justifyContent: "space-around"
    },
    googleButton: {
        backgroundColor: '#FFF',
        borderRadius: ms(20),
        padding: ms(12),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        marginTop: mvs(15),
    },
    buttonText: {
        color: globalTheme.text_head,
        fontWeight: '900',
        fontSize: vs(10),
        textAlign: "center",
        width: '70%',
    },
    updateButton: {
        flex: 1,
        width: '45%',
        backgroundColor: globalTheme.container_dark,
        borderRadius: borderRadius,
        paddingVertical: vs(10),
        paddingHorizontal: ms(10),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
    },
    passwordButton: {
        flex: 1,
        width: '45%',
        backgroundColor: globalTheme.container_medium,
        borderRadius: ms(30),
        paddingVertical: mvs(10),
        paddingHorizontal: ms(10),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
    },
    headerContainer: {
        alignItems: 'center',
        marginBottom: mvs(20),
    },
    avatarCircle: {
        width: scale(90),
        height: scale(90),
        borderRadius: ms(45),
        backgroundColor: '#ccc',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: mvs(10),
        overflow: 'hidden',
    },
    avatarImage: {
        width: '100%',
        height: '100%',
        borderRadius: ms(45),
    },
    bodyImage: {
        zIndex: 3,
        position:"absolute",
        alignSelf: "center",
        marginTop: -vs(40)
    },
});
