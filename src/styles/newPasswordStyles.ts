// src/styles/newPasswordStyles.ts

import { StyleSheet } from 'react-native';
import { vs, mvs } from 'react-native-size-matters';
import { globalTheme } from '../constants/theme';

export const newPasswordStyles = StyleSheet.create({
    wrapper: {
        flex: 1,
        alignItems: "center",
        paddingTop: vs(50)
    },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: mvs(20),
        width: '100%',
    },
    form: {
        backgroundColor: globalTheme.container_translucent, // Mismo que ForgotPassword
        borderRadius: 25, // Mismo borderRadius que el tema
        padding: mvs(25),
        width: '80%',
        alignSelf: 'center', // Centrar el form, no su contenido
        shadowColor: globalTheme.container_dark,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    inputContainer: {
        position: 'relative',
        width: '100%',
        marginBottom: vs(20),
        flexDirection: 'row', // Para mantener el ancho estable
        alignItems: 'center',
    },
    input: {
        flex: 1, // Usar flex en lugar de width: '100%'
        backgroundColor: globalTheme.input, // Mismo que ForgotPassword
        borderRadius: 25, // Mismo borderRadius que el tema
        paddingVertical: vs(15),
        paddingHorizontal: vs(20),
        paddingRight: vs(50), // Espacio para el icono
        fontSize: vs(14), // Más pequeña (era 16)
        color: globalTheme.text_contrast,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.3)',
        shadowColor: globalTheme.container_dark,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
        minHeight: vs(50), // Altura mínima fija
    },
    inputFocused: {
        borderColor: globalTheme.gradient[0], // Verde del tema cuando está enfocado
        shadowOpacity: 0.15,
        elevation: 4,
    },
    iconOverlay: {
        position: 'absolute',
        right: vs(16),
        top: '50%',
        transform: [{ translateY: -12 }],
        padding: vs(6),
        borderRadius: vs(20),
        backgroundColor: 'rgba(56, 89, 39, 0.05)', // Fondo más suave para el icono
    },
    loginBtn: {
        backgroundColor: globalTheme.gradient[0], // Verde del tema
        borderRadius: 25, // Mismo borderRadius que el tema
        paddingVertical: vs(15),
        paddingHorizontal: vs(30),
        alignItems: 'center',
        marginTop: mvs(25),
        width: '100%',
        shadowColor: globalTheme.gradient[0],
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 6,
        elevation: 3,
    },
    buttonText: {
        color: globalTheme.text_head,
        fontSize: vs(14), // Más pequeña
        fontWeight: 'bold',
        textAlign: 'center',
    },
    // Estilos adicionales para mejorar la experiencia
    titleContainer: {
        marginBottom: mvs(25),
        width: '100%',
        alignItems: 'center', // Solo centrar el texto, no afectar inputs
    },
    title: {
        color: globalTheme.text_head,
        fontSize: vs(16), // Más pequeña
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: vs(8),
        textTransform: 'uppercase',
        letterSpacing: 1,
    },
    subtitle: {
        color: globalTheme.text_head,
        fontSize: vs(11), // Más pequeña
        textAlign: 'center',
        marginBottom: vs(18),
        opacity: 0.9,
        lineHeight: vs(16),
    },
    // Estilos no utilizados pero mantenidos para compatibilidad
    card: {
        backgroundColor: 'transparent',
    },
    enhancedInput: {
        shadowColor: globalTheme.container_dark,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 3,
    },
    button: {
        backgroundColor: globalTheme.gradient[0],
        paddingVertical: mvs(6),
        borderRadius: 30,
        alignItems: 'center',
        marginTop: mvs(10),
    },
    link1: {
        color: globalTheme.gradient[0],
        fontSize: vs(12),
        textAlign: 'center',
        textDecorationLine: 'underline',
        fontWeight: '500',
    },
    privacyLink: {
        alignSelf: 'center',
        marginTop: mvs(20),
        paddingVertical: vs(8),
        paddingHorizontal: vs(12),
    },
});
