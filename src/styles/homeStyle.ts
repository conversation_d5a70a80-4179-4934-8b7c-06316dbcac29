import { StyleSheet, Dimensions } from 'react-native';
import { vs} from 'react-native-size-matters';
import {borderRadius, globalTheme} from "../constants/theme";

const imgGrid = (Dimensions.get('window').width * 0.8) / 3;

export const homeStyles = StyleSheet.create({

    sectionTitle: {
        fontSize: vs(16),
        fontWeight: 'bold',
        color: globalTheme.text_title,
        textAlign: 'center',
        width: '80%',
        alignSelf: "center"
    },
    postImage: {
        width: vs(100),
        height: vs(100),
        borderRadius: borderRadius,
        marginRight: vs(8),
        backgroundColor: '#fff',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.25,
        shadowRadius: 4.5,
        elevation: 6,
    },
    bannerWrapper: {
        width: '90%',
        aspectRatio: 16 / 9,
        borderRadius: 12,
        overflow: 'hidden',
        backgroundColor: 'red',
        objectFit: "cover"
    },
    card: {
        marginRight: 15,
        alignItems: 'center',
        width: 140,
    },
    cardImage: {
        width: imgGrid,
        height: imgGrid,
        borderRadius: 10,
        backgroundColor: globalTheme.background,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 2,
    },
    cardOverlay: {
        paddingVertical: vs(3),
    },
    cardText: {
        color: globalTheme.text_title,
        fontSize: vs(12),
        textAlign: 'center',
        fontWeight: 'bold',
    },
    gridContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-evenly',
        width: '90%',
        alignSelf: "center",
    },
    gridItem: {
        width: imgGrid,
        borderRadius: 8,
        overflow: 'hidden',
    },
    colapsableContainer: {
        width: '100%',
        backgroundColor: 'rgba(209,209,209,0.5)',
        paddingHorizontal: vs(8)
    },
    imageInfo: {
        position: 'absolute',
        zIndex: 2,
        bottom: vs(30),
        left: vs(25)
    },
    infoTitle: {
        color: 'white',
        textTransform: 'uppercase',
        fontSize: vs(18),
        fontWeight: '900'
    },
    infoDate: {
        color: 'white',
        textTransform: "uppercase",
        fontSize: vs(10)
    }
});
