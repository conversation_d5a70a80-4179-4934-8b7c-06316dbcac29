import { StyleSheet, Dimensions } from 'react-native';
import {vs} from "react-native-size-matters";

const { width } = Dimensions.get('window');
const cardWidth = width * 0.85;
const avatarSize = vs(80);
const logoWidth = width * 0.35;
const signatureDimensions = vs(60)

export const credentialStyles = StyleSheet.create({
    backTop: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        marginTop: vs(30),
        gap: 6,
    },
    backText: {
        color: 'white',
        fontWeight: 'semibold',
        fontSize: vs(14)
    },
    card: {
        marginTop: vs(60),
        width: cardWidth,
        height: vs(485),
        backgroundColor: '#e6edf4',
        borderRadius: vs(25),
        alignItems: 'center',
        alignSelf: 'center',
        overflow: "hidden",
    },
    cardContainer: {
        position: "absolute",
        zIndex: 2,
        width: '100%',
        height: '100%',
        alignItems: "center",
        justifyContent: "space-between",
        paddingTop: vs(30),
        paddingBottom: vs(15),
    },
    containerBody: {
        width: '100%',
        marginTop: vs(50),
        paddingTop: vs(50),
        paddingBottom: vs(15)
    },
    bodyImage: {
        zIndex: 3,
        position:"absolute",
        alignSelf: "center",
        marginTop: -vs(40)
    },
    logo: {
        width: logoWidth,
        height: logoWidth * 0.4, // Relación de aspecto 2.5:1
        marginBottom: 16,
    },
    avatar: {
        width: avatarSize,
        height: avatarSize,
        borderRadius: avatarSize / 2,
        marginBottom: 16,
        backgroundColor: '#ccc',
    },
    name: {
        fontSize: width * 0.05, // 5% del ancho
        fontWeight: 'bold',
        marginBottom: 4,
        textAlign: 'center',
    },
    label: {
        color: '#FFF',
        fontSize: vs(15),
        fontWeight: "bold",
        textAlign: "center"
    },
    info: {
        fontSize: vs(13),
        color: '#000',
        marginBottom: 4,
        textAlign: 'center',
        backgroundColor: '#FFF',
        width: '80%',
        alignSelf: "center",
        borderRadius: vs(25)
    },
    qrBox: {
        marginTop: 20,
        backgroundColor: 'white',
        padding: 12,
        borderRadius: 12,
    },
    signature: {
        width: signatureDimensions,
        height: signatureDimensions
    },
    qrButton: {
        backgroundColor: '#0077cc',
        paddingVertical: vs(3),
        paddingHorizontal: vs(20),
        borderRadius: vs(25)
    }
});
