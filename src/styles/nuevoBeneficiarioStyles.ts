// styles/nuevoBeneficiarioStyles.ts

import { StyleSheet } from 'react-native';
import {vs} from "react-native-size-matters";

export const nuevoBeneficiarioStyles = StyleSheet.create({
    scrollContainer: {
        flexGrow: 1,
        paddingTop: vs(20),
        paddingBottom: vs(40),
        alignItems: 'center',
    },
    wrapper: {
        flex: 1,
        position: 'relative',
    },
    container: {
        backgroundColor: 'rgba(218,214,214,0.34)',
        borderRadius: vs(25),
        padding: vs(15),
        paddingTop: vs(25),
        alignSelf: "center",
        marginVertical: vs(20),
        width: '85%',
    },
    header: { fontSize: 22, fontWeight: 'bold', marginBottom: 16, color: '#FFF', textAlign: "center" },
    avatarContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    avatarCircle: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: '#007AFF',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    avatarText: {
        color: 'white',
        fontSize: 24,
    },
    photoButton: {
        backgroundColor: '#53A9FF',
        paddingVertical: 6,
        paddingHorizontal: 16,
        borderRadius: 12,
    },
    photoButtonText: {
        color: 'white',
        fontWeight: 'bold',
    },
    input: {
        backgroundColor: 'white',
        borderRadius: 30,
        paddingVertical: 12,
        paddingHorizontal: 20,
        marginBottom: 12,
        fontSize: 16,
        color: '#333',
    },
    saveButton: {
        backgroundColor: '#008E0E',
        paddingVertical: 14,
        borderRadius: 30,
        alignItems: 'center',
        marginTop: 12,
    },
    saveButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    regresar: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'center',
        gap: 8,
        alignContent: 'center',
    },
    regresarText: {
        color: 'white',
        textDecorationLine: 'underline',
        fontSize: vs(14),
    },
    agregar: {
        backgroundColor: '#02AF14',
        borderRadius: vs(25),
        flexDirection: "row",
        width: '65%',
        alignSelf: "center",
        justifyContent: "center",
        alignItems: "center",
        gap: 10,
        marginBottom: vs(15),
        paddingVertical: vs(1)
    },
});
