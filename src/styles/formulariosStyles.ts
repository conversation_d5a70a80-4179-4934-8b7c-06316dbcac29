import { StyleSheet } from 'react-native';
import { borderRadius, globalTheme } from "../constants/theme";
import { vs } from "react-native-size-matters";

export const styles = StyleSheet.create({
  container: {
    height: '100%',
    backgroundColor: globalTheme.background,
    padding: vs(16),
    paddingTop: vs(20),
  },
  title: {
    fontWeight: 'bold',
    fontSize: 20,
    alignSelf: 'center',
    marginBottom: 16,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: globalTheme.text_light,
    borderRadius: borderRadius,
    padding: 14,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 4,
  },
  iconBox: {
    backgroundColor: 'transparent',
    borderRadius: 10,
    padding: 8,
    marginRight: 16,
  },
  itemText: {
    flex: 1,
    fontSize: 16,
    color: '#222',
    textTransform: 'uppercase'
  },
  chevron: {
    marginLeft: 8,
  },
  // Estilos para el formulario individual
  formContainer: {
    width: '85%',
    backgroundColor: globalTheme.container_translucent,
    padding: vs(20),
    paddingVertical: vs(25),
    alignSelf: "center",
    borderRadius: borderRadius,
    marginTop: vs(20),

  },
  formContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formTitle: {
    fontWeight: 'bold',
    fontSize: vs(18),
    color: globalTheme.text_head,
    textAlign: 'center',
    marginBottom: vs(5),
    textTransform: "uppercase"
  },
  formDescription: {
    fontSize: vs(12),
    color: globalTheme.text_head,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },

  submitButton: {
    backgroundColor: globalTheme.button_ok,
    borderRadius: borderRadius,
    padding: vs(8),
    alignItems: 'center',
    marginTop: vs(20),
    justifyContent: "center",
    elevation: 3
  },
  submitButtonText: {
    color: globalTheme.text_head,
    fontWeight: 'bold',
    fontSize: vs(16),
  },
  submittingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButton: {
    alignItems: 'center',
    marginTop: 16,
    padding: 12,
    flexDirection: 'row',
    alignSelf: "center"
  },
  backButtonText: {
    color: globalTheme.text_head,
    fontSize: 16,
    fontWeight: '500',
  },
  // Estilos para estado vacío
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Estilos para estado de carga
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  // Estilos para cuando no hay campos
  noFieldsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    backgroundColor: globalTheme.gradient[1],
    borderRadius: 12,
    marginVertical: 20,
  },
  noFieldsText: {
    fontSize: 16,
    color: globalTheme.text_head,
    textAlign: 'center',
    fontWeight: '500',
  },
  // Estilos para formulario enviado
  submittedBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  submittedTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  submittedTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#065F46',
    marginBottom: 4,
  },
  submittedSubtitle: {
    fontSize: 14,
    color: '#047857',
  },
  // Estilos para cuando no puede enviar
  cannotSubmitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFBEB',
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  cannotSubmitText: {
    fontSize: 14,
    color: '#92400E',
    marginLeft: 12,
    flex: 1,
  },
});
