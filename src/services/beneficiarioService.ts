import axios from 'axios';
import { API_URL } from '../constants/config';
import { normalizeImageUrl } from '../utils/imageUtils';

export const getBeneficiarios = async (userId: number) => {
    try {
        console.log('📋 getBeneficiarios - Iniciando obtención');
        console.log('👤 userId:', userId);

        const endpoint = `${API_URL}/users/${userId}/beneficiary`;
        console.log('🌐 Endpoint:', endpoint);

        const {data} = await axios.get(endpoint);
        console.log('✅ getBeneficiarios - Respuesta exitosa:', data);

        // Normalizar URLs de fotos de beneficiarios (igual que ProfileService)
        if (data.beneficiaries && Array.isArray(data.beneficiaries)) {
            data.beneficiaries = data.beneficiaries.map((beneficiario: any) => {
                if (beneficiario.photo) {
                    console.log('🔧 Normalizando foto de beneficiario:', beneficiario.photo);
                    beneficiario.photo = normalizeImageUrl(beneficiario.photo);
                    console.log('✅ Foto normalizada:', beneficiario.photo);
                }
                return beneficiario;
            });
        }

        return {error: false, data: data.beneficiaries};
    } catch (error: any) {
        console.error('❌ Error en getBeneficiarios:');
        console.error('🔍 Error completo:', error);
        console.error('📡 Response data:', error.response?.data);
        console.error('💬 Error message:', error.message);

        return {
            error: true,
            msg: error?.response?.data?.message || 'Error al obtener beneficiarios',
        };
    }
};

// Función genérica para crear o actualizar beneficiarios
const handleBeneficiarioRequest = async (userId: number, beneficiarioId: number | null, payload: any, method: string) => {
    try {
        console.log('🔄 handleBeneficiarioRequest - Iniciando proceso');
        console.log('👤 userId:', userId);
        console.log('🆔 beneficiarioId:', beneficiarioId);
        console.log('📝 payload original:', payload);
        console.log('🔧 method:', method);

        // Determinar el endpoint según si es creación o actualización
        const endpoint = beneficiarioId
            ? `${API_URL}/users/${userId}/beneficiary/${beneficiarioId}`  // Actualización
            : `${API_URL}/users/${userId}/beneficiary`;  // Creación

        console.log('🌐 Endpoint:', endpoint);

        // Verificar si hay una foto para subir (misma lógica que profileService)
        const hasPhoto = payload.photo &&
                         typeof payload.photo === 'string' &&
                         payload.photo.trim() !== '' &&
                         payload.photo.startsWith('file://');

        console.log('🔍 Verificación de foto en beneficiario:');
        console.log('  - payload.photo existe:', !!payload.photo);
        console.log('  - es string:', typeof payload.photo === 'string');
        console.log('  - no está vacía:', payload.photo ? payload.photo.trim() !== '' : false);
        console.log('  - es file://:', payload.photo ? payload.photo.startsWith('file://') : false);
        console.log('  - hasPhoto final:', hasPhoto);

        if (hasPhoto) {
            console.log('📸 Detectada foto para subir:', payload.photo);
            // Crear FormData solo si hay foto
            console.log('📦 Creando FormData con foto...');
            const formDataObj = new FormData();

            // Agregar todos los campos al FormData
            console.log('📝 Agregando campos al FormData:');
            Object.entries(payload).forEach(([key, value]) => {
                if (key !== 'photo') {
                    console.log(`  - ${key}: ${value}`);
                    formDataObj.append(key, value as string);
                }
            });

            // Agregar la foto con mejor detección de nombre y tipo
            const uriParts = payload.photo.split('/');
            const fileName = uriParts[uriParts.length - 1] || 'photo.jpg';
            const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'jpg';

            // Determinar tipo MIME basado en extensión
            let mimeType = 'image/jpeg';
            if (fileExtension === 'png') mimeType = 'image/png';
            else if (fileExtension === 'gif') mimeType = 'image/gif';
            else if (fileExtension === 'webp') mimeType = 'image/webp';

            console.log('📸 Agregando foto:', {
                fileName,
                fileExtension,
                mimeType,
                uri: payload.photo.substring(0, 50) + '...'
            });

            const photoFile = {
                uri: payload.photo,
                name: fileName,
                type: mimeType,
            };

            console.log('📤 Objeto de foto a enviar:', photoFile);
            formDataObj.append('photo', photoFile as any);

            // Log del FormData completo
            console.log('📋 FormData completo enviado al backend:');
            // @ts-ignore
            for (let [key, value] of formDataObj.entries()) {
                if (typeof value === 'object' && value.uri) {
                    console.log(`  - ${key}: [ARCHIVO] ${value.name} (${value.type})`);
                } else {
                    console.log(`  - ${key}: ${value}`);
                }
            }

            // Enviar con FormData
            console.log('📤 Enviando FormData...');
            console.log('🔧 Headers:', { 'Content-Type': 'multipart/form-data' });

            const response = await axios({
                method: method,
                url: endpoint,
                data: formDataObj,
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log('✅ Respuesta exitosa (con foto):', response.status, response.data);
            return response.data;
        } else {
            // Sin foto, enviar JSON normal
            console.log('❌ No se detectó foto para subir');
            console.log('🔍 Razones posibles:');
            console.log('  - formData.photo es falsy:', !payload.photo);
            console.log('  - No empieza con file://:', payload.photo && !payload.photo.startsWith('file://'));
            console.log('  - Valor actual de photo:', payload.photo);

            const cleanPayload = { ...payload };
            delete cleanPayload.photo; // Remover photo si está vacía
            console.log('🧹 cleanPayload (sin foto):', cleanPayload);

            console.log('📤 Enviando JSON...');
            console.log('🔧 Headers:', { 'Content-Type': 'application/json' });

            const response = await axios({
                method: method,
                url: endpoint,
                data: cleanPayload,
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            console.log('✅ Respuesta exitosa (sin foto):', response.status, response.data);
            return response.data;
        }
    } catch (error: any) {
        console.error("❌ Error en beneficiario:");
        console.error("🔍 Error completo:", error);
        console.error("📡 Response status:", error.response?.status);
        console.error("📡 Response headers:", error.response?.headers);
        console.error("📡 Response data:", error.response?.data);
        console.error("🌐 Request config:", error.config);
        console.error("💬 Error message:", error.message);
        console.error("🔗 Request URL:", error.config?.url);
        console.error("🔧 Request method:", error.config?.method);
        console.error("📤 Request data:", error.config?.data);

        // Análisis específico del error 500
        if (error.response?.status === 500) {
            console.error("🚨 ERROR 500 DETECTADO:");
            console.error("  - Esto indica un error interno del servidor");
            console.error("  - El problema está en el backend, no en el frontend");
            console.error("  - Revisar logs del servidor para más detalles");
            console.error("  - Datos enviados que causaron el error:", error.config?.data);
        }

        const errorMsg = error?.response?.data?.message || error?.response?.data?.error?.message || error.message || 'Error en la solicitud';
        console.error("📝 Mensaje final de error:", errorMsg);

        return {
            error: true,
            msg: errorMsg,
        };
    }
};

// Función para crear un beneficiario
export const createBeneficiario = async (userId: number, payload: any) => {
    console.log('➕ createBeneficiario - Iniciando creación');
    console.log('👤 userId:', userId);
    console.log('📝 payload:', payload);

    const result = await handleBeneficiarioRequest(userId, null, payload, 'POST');  // 'POST' para crear

    console.log('📥 createBeneficiario - Resultado:', result);
    return result;
};

// Función para actualizar un beneficiario
export const updateBeneficiario = async (userId: number, beneficiarioId: number, payload: any) => {
    console.log('🔄 updateBeneficiario - Iniciando actualización');
    console.log('👤 userId:', userId);
    console.log('🆔 beneficiarioId:', beneficiarioId);
    console.log('📝 payload:', payload);

    const result = await handleBeneficiarioRequest(userId, beneficiarioId, payload, 'POST');  // 'PUT' para actualizar

    console.log('📥 updateBeneficiario - Resultado:', result);
    return result;
};

// Función para actualizar solo la foto de un beneficiario
export const updateBeneficiaryPhoto = async (userId: number, beneficiaryId: number, photoUri: string) => {
    try {
        console.log('📸 updateBeneficiaryPhoto - Iniciando actualización de foto');
        console.log('👤 userId:', userId);
        console.log('🆔 beneficiaryId:', beneficiaryId);
        console.log('📷 photoUri:', photoUri);

        // Verificar que tenemos una foto válida
        if (!photoUri || !photoUri.startsWith('file://')) {
            console.error('❌ URI de foto inválida:', photoUri);
            return {
                error: true,
                msg: 'URI de foto inválida. Debe ser una ruta de archivo local.',
            };
        }

        const endpoint = `${API_URL}/users/${userId}/beneficiary/${beneficiaryId}/photo`;
        console.log('🌐 Endpoint:', endpoint);

        // Crear FormData para la foto
        const formDataObj = new FormData();

        // Extraer nombre del archivo y determinar tipo MIME
        const uriParts = photoUri.split('/');
        const fileName = uriParts[uriParts.length - 1] || 'photo.jpg';
        const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'jpg';

        // Determinar tipo MIME basado en extensión
        let mimeType = 'image/jpeg';
        if (fileExtension === 'png') mimeType = 'image/png';
        else if (fileExtension === 'gif') mimeType = 'image/gif';
        else if (fileExtension === 'webp') mimeType = 'image/webp';

        console.log('📸 Detalles del archivo:', {
            fileName,
            fileExtension,
            mimeType,
            uri: photoUri.substring(0, 50) + '...'
        });

        const photoFile = {
            uri: photoUri,
            name: fileName,
            type: mimeType,
        };

        console.log('📤 Objeto de foto a enviar:', photoFile);
        formDataObj.append('photo', photoFile as any);

        // Log del FormData
        console.log('📋 FormData enviado al backend:');
        // @ts-ignore
        for (let [key, value] of formDataObj.entries()) {
            if (typeof value === 'object' && value.uri) {
                console.log(`  - ${key}: [ARCHIVO] ${value.name} (${value.type})`);
            } else {
                console.log(`  - ${key}: ${value}`);
            }
        }

        // Enviar la solicitud
        console.log('📤 Enviando FormData...');
        const response = await axios.post(endpoint, formDataObj, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        console.log('✅ Respuesta exitosa:', response.status, response.data);

        // Normalizar la URL de la foto en la respuesta si existe
        if (response.data.beneficiary && response.data.beneficiary.photo) {
            console.log('🔧 Normalizando foto en respuesta:', response.data.beneficiary.photo);
            response.data.beneficiary.photo = normalizeImageUrl(response.data.beneficiary.photo);
            console.log('✅ Foto normalizada:', response.data.beneficiary.photo);
        }

        return {
            error: false,
            data: response.data,
            msg: response.data.message || 'Foto actualizada correctamente',
        };

    } catch (error: any) {
        console.error('❌ Error en updateBeneficiaryPhoto:');
        console.error('🔍 Error completo:', error);
        console.error('📡 Response status:', error.response?.status);
        console.error('📡 Response data:', error.response?.data);
        console.error('💬 Error message:', error.message);

        const errorMsg = error?.response?.data?.message ||
                        error?.response?.data?.error ||
                        error.message ||
                        'Error al actualizar la foto del beneficiario';

        return {
            error: true,
            msg: errorMsg,
        };
    }
};

// Función para eliminar un beneficiario
export const deleteBeneficiario = async (userId: number, beneficiarioId: number, token: string) => {
    try {
        const response = await fetch(`${API_URL}/users/${userId}/beneficiary/${beneficiarioId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        const data = await response.json();
        return {
            error: !response.ok,
            msg: data.message || 'Error inesperado',
        };
    } catch (error) {
        return {
            error: true,
            msg: 'Error de red o servidor',
        };
    }
};
