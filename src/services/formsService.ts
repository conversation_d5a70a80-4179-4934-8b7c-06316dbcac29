// Funciones actualizadas para React Native API
import { API_URL } from '../constants/config';
import { prepareFormDataForSubmission, validateFormValues } from './fileService';
import axios from 'axios';

export async function listForms(tenant: string, token: string) {
  console.log('[listForms] tenant:', tenant, 'token:', token);
  try {
    const url = `${API_URL.replace(/\/$/, '')}/forms`;
    console.log('[listForms] GET', url);
    const { data } = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    console.log('[listForms] response:', data);
    return data.forms;
  } catch (error: any) {
    console.log('[listForms] error:', error);
    const msg = error.response?.data?.error || error.message;
    throw new Error(msg);
  }
}

export async function getFormDetails(tenant: string, token: string, formId: number) {
  console.log('[getFormDetails] tenant:', tenant, 'token:', token, 'formId:', formId);
  try {
    const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}`;
    console.log('[getFormDetails] GET FormDetails', url);
    const { data } = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    console.log('[getFormDetails] response:', data);

    // Log específico para valores de archivos
    if (data.user_submission_status?.submission?.values) {
      console.log('[getFormDetails] submission values:', JSON.stringify(data.user_submission_status.submission.values, null, 2));
    }

    return data;
  } catch (error: any) {
    console.log('[getFormDetails] error:', error);
    const msg = error.response?.data?.error || error.message;
    throw new Error(msg);
  }
}

export async function submitForm(
  tenant: string,
  token: string,
  formId: number,
  values: Record<string, any>
) {
  console.log('[submitForm] tenant:', tenant, 'token:', token, 'formId:', formId, 'values:', values);

  // Verificar si hay archivos para subir
  const hasFiles = Object.values(values).some(value =>
    typeof value === 'string' && (value.startsWith('file://') || value.startsWith('content://'))
  );

  if (hasFiles) {
    console.log('📎 Detectados archivos para subir, usando FormData');

    // Validar valores del formulario - TEMPORAL: Solo permitir file:// URIs
    const validation = validateFormValues(values);
    if (validation.warnings.length > 0) {
      console.warn('⚠️ Advertencias en formulario:', validation.warnings);
    }
    if (!validation.isValid) {
      throw new Error(`Errores en formulario: ${validation.errors.join(', ')}`);
    }

    // TEMPORAL: Rechazar content:// URIs si react-native-fs no está disponible
    const hasContentUris = Object.values(values).some(value =>
      typeof value === 'string' && value.startsWith('content://')
    );
    if (hasContentUris) {
      // Verificar si react-native-fs está disponible
      try {
        const RNFS = require('react-native-fs');
        if (!RNFS || typeof RNFS.copyFile !== 'function') {
          throw new Error('react-native-fs no disponible');
        }
      } catch (error) {
        throw new Error('Por el momento solo se pueden subir fotos desde la cámara o galería. Los documentos requieren rebuildar la aplicación.');
      }
    }

    // Usar la nueva función para preparar FormData correctamente (ahora es asíncrona)
    const formData = await prepareFormDataForSubmission(values);

    // FormData ya está preparado con logs internos

    try {
      const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}/submit`;
      console.log('[submitForm] POST (con archivos)', url);
      const { data } = await axios.post(url, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000, // 30 segundos para uploads
      });
      console.log('[submitForm] response (con archivos):', data);
      return data;
    } catch (error: any) {
      console.log('[submitForm] error (con archivos):', error);
      console.log('[submitForm] error.response:', error.response);
      console.log('[submitForm] error.response.data:', error.response?.data);
      console.log('[submitForm] error.response.status:', error.response?.status);

      // Manejo especial para cuando el usuario ya ha enviado el formulario
      if (error.response?.status === 409) {
        const errorData = error.response.data;
        throw new Error(JSON.stringify({
          type: 'ALREADY_SUBMITTED',
          message: errorData.error,
          existingSubmissionId: errorData.existing_submission_id,
          submittedAt: errorData.submitted_at
        }));
      }

      // Manejo específico de errores de content URI
      if (error.response?.data?.error === 'Android content URI detected') {
        throw new Error('Hay un problema con el archivo seleccionado. Por favor, selecciona el archivo nuevamente.');
      }

      // Manejo de errores de permisos del servidor
      if (error.response?.data?.message?.includes('Permission denied')) {
        throw new Error('Error del servidor: No se pueden guardar archivos. Contacta al administrador.');
      }

      // Manejo de errores de upload de archivos
      if (error.response?.data?.error === 'Error uploading file') {
        throw new Error('Error subiendo archivo. El archivo puede estar dañado o ser de un tipo no soportado. Intenta con otro archivo.');
      }

      // Manejo de errores relacionados con react-native-fs
      if (error.message?.includes('react-native-fs') || error.message?.includes('RNSFILETYPEREGULAR')) {
        throw new Error('Error de compatibilidad de archivos. Por favor, usa solo fotos de la cámara o galería por el momento.');
      }

      const msg = error.response?.data?.error || error.message;
      throw new Error(msg);
    }
  } else {
    console.log('📄 Sin archivos detectados, usando JSON');
    // Sin archivos, usar JSON normal
    const processedValues: Record<string, any> = {};
    Object.entries(values).forEach(([key, value]) => {
      processedValues[key] = Array.isArray(value) ? JSON.stringify(value) : value;
    });

    try {
      const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}/submit`;
      console.log('[submitForm] POST', url, 'body:', { values: processedValues });
      const { data } = await axios.post(
        url,
        { values: processedValues },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      console.log('[submitForm] response:', data);
      return data;
    } catch (error: any) {
      console.log('[submitForm] error:', error);
      console.log('[submitForm] error.response:', error.response);
      console.log('[submitForm] error.response.data:', error.response?.data);
      console.log('[submitForm] error.response.status:', error.response?.status);

      // Manejo especial para cuando el usuario ya ha enviado el formulario
      if (error.response?.status === 409) {
        const errorData = error.response.data;
        throw new Error(JSON.stringify({
          type: 'ALREADY_SUBMITTED',
          message: errorData.error,
          existingSubmissionId: errorData.existing_submission_id,
          submittedAt: errorData.submitted_at
        }));
      }

      // Para errores 500, agregar más contexto
      if (error.response?.status === 500) {
        console.log('[submitForm] Server error 500 - Request details:');
        console.log('- URL:', `${API_URL.replace(/\/$/, '')}/forms/${formId}/submit`);
        console.log('- Processed values:', processedValues);
        console.log('- Original values:', values);
      }

      const msg = error.response?.data?.error || error.message;
      throw new Error(msg);
    }
  }
}

// Nueva función para obtener la respuesta existente del usuario
export async function getMySubmission(
  tenant: string,
  token: string,
  formId: number
) {
  console.log('[getMySubmission] tenant:', tenant, 'token:', token, 'formId:', formId);
  try {
    const url = `${API_URL.replace(/\/$/, '')}/forms/${formId}/my-submission`;
    console.log('[getMySubmission] GET', url);
    const { data } = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    console.log('[getMySubmission] response:', data);
    return data;
  } catch (error: any) {
    console.log('[getMySubmission] error:', error);
    const msg = error.response?.data?.error || error.message;
    throw new Error(msg);
  }
}

// Función helper para verificar si el usuario puede enviar el formulario
export function canUserSubmitForm(formDetails: any): boolean {
  return formDetails.user_submission_status?.can_submit === true;
}

// Función helper para verificar si el usuario ya envió el formulario
export function hasUserSubmittedForm(formDetails: any): boolean {
  return formDetails.user_submission_status?.has_submitted === true;
}

// Función helper para obtener información de la respuesta del usuario
export function getUserSubmissionInfo(formDetails: any): any {
  return formDetails.user_submission_status?.submission || null;
}
