// src/services/userService.ts
import axios from 'axios';
import { API_URL } from '../constants/config';
import * as SecureStore from 'expo-secure-store';
import { normalizeImageUrl } from '../utils/imageUtils';

export interface ApiResponse {
    error: boolean;
    profile?: {
        curp: string;
        employee_number: any;
        company_id: any;
        user_id: any;
        name?: string;
        last_name?: string;
        phone_number?: string;
        email?: string;
        photo?: string;
    };
    msg?: string;
}


export const getProfile = async (): Promise<ApiResponse> => {
    try {
        console.log('🔄 getProfile - Iniciando obtención de perfil');
        const userId = await SecureStore.getItemAsync('user_id');
        console.log('🆔 userId desde SecureStore:', userId);

        if (!userId) {
            console.error('❌ No se encontró userId en SecureStore');
            return {
                error: true,
                msg: 'No se encontró el ID del usuario en SecureStore.'
            };
        }

        const endpoint = `${API_URL}/users/${userId}/profile`;
        console.log('🌐 Endpoint getProfile:', endpoint);

        const { data } = await axios.get(endpoint);
        console.log('✅ Respuesta getProfile exitosa:', data);

        // Normalizar URL de foto si existe
        if (data.profile && data.profile.photo) {
            console.log('🔧 Normalizando foto de perfil:', data.profile.photo);
            data.profile.photo = normalizeImageUrl(data.profile.photo);
            console.log('✅ Foto normalizada:', data.profile.photo);
        }

        return {
            error: false,
            profile: data.profile
        };
    } catch (error: any) {
        console.error("❌ Error al obtener el perfil:");
        console.error("🔍 Error completo:", error);
        console.error("📡 Response status:", error.response?.status);
        console.error("📡 Response data:", error.response?.data);
        console.error("💬 Error message:", error.message);

        return {
            error: true,
            msg: "No se pudo obtener el perfil."
        };
    }
};


/**
 * Limpia un objeto eliminando las claves con valores `undefined`
 */
const cleanPayload = (data: Record<string, any>) =>
    Object.fromEntries(Object.entries(data).filter(([_, v]) => v !== undefined));

export const updateProfile = async (userId: number, formData: Record<string, any>) => {
    try {
        console.log('🔄 updateProfile - Iniciando actualización');
        console.log('👤 userId:', userId);
        console.log('📝 formData original:', formData);

        const cleanedFormData = cleanPayload(formData);
        console.log('🧹 cleanedFormData:', cleanedFormData);

        // Check if we have a photo to upload
        console.log('🔍 Analizando foto en formData:');
        console.log('  - formData.photo:', formData.photo);
        console.log('  - Tipo:', typeof formData.photo);
        console.log('  - Es string:', typeof formData.photo === 'string');
        console.log('  - Empieza con file://:', formData.photo && formData.photo.startsWith('file://'));
        console.log('  - Longitud:', formData.photo ? formData.photo.length : 'N/A');

        if (formData.photo && formData.photo.startsWith('file://')) {
            console.log('📸 ✅ Detectada foto para subir:', formData.photo);
            // Create a FormData object for multipart/form-data
            const formDataObj = new FormData();

            // Mapear campos del frontend a los nombres que espera el backend
            const fieldMapping = {
                'name': 'name',                    // ✅ Correcto
                'last_name': 'last_name',          // ✅ Correcto
                'phone_number': 'phone_number',    // ✅ Correcto
                'email': 'email',                  // ✅ Correcto
                'employee_number': 'employee_number', // ✅ Correcto
                'company_id': 'company_id',        // ✅ Correcto
            };

            // Append all form fields with correct names
            Object.entries(cleanedFormData).forEach(([key, value]) => {
                if (key !== 'photo') {
                    const backendFieldName = fieldMapping[key] || key;
                    console.log(`📝 Mapeando campo: ${key} -> ${backendFieldName} = ${value}`);
                    formDataObj.append(backendFieldName, value as string);
                }
            });

            // Append the photo file
            // Extract filename from the URI
            const uriParts = formData.photo.split('/');
            const fileName = uriParts[uriParts.length - 1];

            console.log('📁 Detalles del archivo:');
            console.log('  - URI completa:', formData.photo);
            console.log('  - Nombre del archivo:', fileName);
            console.log('  - Partes de la URI:', uriParts);

            const photoFile = {
                uri: formData.photo,
                name: fileName,
                type: 'image/jpeg', // Assuming JPEG format, adjust if needed
            };

            console.log('📤 Objeto de foto a enviar:', photoFile);
            formDataObj.append('photo', photoFile as any);

            // Log del FormData completo
            console.log('📋 FormData completo enviado al backend:');
            // @ts-ignore
            for (let [key, value] of formDataObj.entries()) {
                if (typeof value === 'object' && value.uri) {
                    console.log(`  - ${key}: [ARCHIVO] ${value.name} (${value.type})`);
                } else {
                    console.log(`  - ${key}: ${value}`);
                }
            }

            // Send the request with FormData
            const endpoint = `${API_URL}/users/${userId}/profile`;
            console.log('🌐 Endpoint (con foto):', endpoint);
            console.log('📤 Enviando FormData con foto...');

            const response = await axios.post(endpoint, formDataObj, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            console.log('✅ Respuesta exitosa (con foto):', response.status, response.data);

            // Verificar si la foto se guardó correctamente
            if (response.data.user && response.data.user.photo) {
                console.log('🖼️ Foto guardada en backend:', response.data.user.photo);
            } else {
                console.warn('⚠️ La respuesta no incluye foto o está vacía');
                console.log('🔍 Estructura de respuesta:', Object.keys(response.data));
                console.log('🔍 Datos completos de respuesta:', response.data);
            }

            return response.data;
        } else {
            // No photo to upload, use JSON
            console.log('❌ No se detectó foto para subir');
            console.log('🔍 Razones posibles:');
            console.log('  - formData.photo es falsy:', !formData.photo);
            console.log('  - No empieza con file://:', formData.photo && !formData.photo.startsWith('file://'));
            console.log('  - Valor actual de photo:', formData.photo);

            const endpoint = `${API_URL}/users/${userId}/profile`;
            console.log('🌐 Endpoint (sin foto):', endpoint);
            console.log('📤 Enviando JSON sin foto:', cleanedFormData);

            const response = await axios.post(endpoint, cleanedFormData, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            console.log('✅ Respuesta exitosa (sin foto):', response.status, response.data);
            return response.data;
        }
    } catch (error: any) {
        console.error("❌ Error al actualizar el perfil:");
        console.error("🔍 Error completo:", error);
        console.error("📡 Response status:", error.response?.status);
        console.error("📡 Response headers:", error.response?.headers);
        console.error("📡 Response data:", error.response?.data);
        console.error("🌐 Request config:", error.config);
        console.error("💬 Error message:", error.message);

        return {
            error: true,
            msg: error?.response?.data?.message || error?.response?.data?.error?.message || error.message || 'Error desconocido',
        };
    }
};
