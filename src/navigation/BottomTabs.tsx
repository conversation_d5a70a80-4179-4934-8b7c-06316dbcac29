import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

import HomeScreen from '../screens/user/HomeScreen';
import CalendarScreen from '../screens/user/CalendarScreen';
import BenefitsScreen from '../screens/user/BenefitsScreen';
import ProfileScreen from '../screens/user/ProfileScreen';
import FormulariosScreen from '../screens/user/FormulariosScreen';
import {globalTheme} from "../constants/theme";

const Tab = createBottomTabNavigator();

export default function BottomTabs() {
    return (
        <Tab.Navigator
            id={undefined}
            initialRouteName="Inicio"
            screenOptions={({ route }) => ({
                headerShown: false,
                tabBarStyle: {
                    backgroundColor: 'rgb(255,255,255)',
                    borderTopWidth: 0,
                    height: 60,
                },
                tabBarActiveTintColor: globalTheme.tabs_active,
                tabBarInactiveTintColor: globalTheme.tabs_inactive,
                tabBarIcon: ({ focused, color, size }) => {
                    const icons = {
                        Calendario: focused ? ("calendar" as const) : ("calendar-outline" as const),
                        Formularios: focused ? ("create" as const) : ("create-outline" as const),
                        Inicio: focused ? ("home" as const) : ("home-outline" as const),
                        Perfil: focused ? ("person" as const) : ("person-outline" as const),
                        Beneficios: focused ? ("pricetag" as const) : ("pricetag-outline" as const),
                    };
                    const iconName = icons[route.name] || ("alert-circle-outline" as const);
                    return <Ionicons name={iconName} size={size} color={color} />;
                },
            })}
        >
            <Tab.Screen name="Formularios" component={FormulariosScreen} />
            <Tab.Screen name="Calendario" component={CalendarScreen} />
            <Tab.Screen name="Inicio" component={HomeScreen} />
            <Tab.Screen name="Beneficios" component={BenefitsScreen} />
            <Tab.Screen name="Perfil" component={ProfileScreen} />

        </Tab.Navigator>
    );
}
