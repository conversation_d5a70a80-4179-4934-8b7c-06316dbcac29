# ===================================
# iOS SPECIFIC GITIGNORE
# ===================================

# Xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData/
*.hmap
*.ipa
*.xcuserstate
*.xcworkspace
!*.xcworkspace/contents.xcworkspacedata
project.xcworkspace/
.xcode.env.local

# Provisioning profiles
*.mobileprovision
*.provisionprofile

# Certificates and keys (IMPORTANT: Never commit these!)
*.p12
*.p8
*.cer
*.crt
*.key
*.pem

# iOS Simulator
*.app

# Bundle artifacts
*.jsbundle

# CocoaPods
/Pods/
Podfile.lock

# Carthage
Carthage/Build/

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Code Injection
iOSInjectionProject/

# Xcode Patch
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcodeproj/project.xcworkspace/
!*.xcworkspace/contents.xcworkspacedata

# OSX
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db

# Firebase
GoogleService-Info.plist
