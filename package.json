{"name": "ts-movil", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-documents/picker": "^10.1.5", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "axios": "^1.8.4", "expo": "53.0.12", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.5", "expo-brightness": "~13.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.1", "expo-device": "~7.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-local-authentication": "~16.0.4", "expo-notifications": "~0.31.3", "expo-secure-store": "~14.2.3", "expo-system-ui": "~5.0.10", "luxon": "^3.6.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-animatable": "^1.4.0", "react-native-calendars": "^1.1311.0", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-chat": "^2.8.1", "react-native-keyboard-controller": "^1.17.5", "react-native-modal-datetime-picker": "^18.0.0", "react-native-navigation-bar-color": "^2.0.2", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.2", "react-native-sse": "^1.2.1", "react-native-stack-view": "^0.7.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0"}, "private": true, "devDependencies": {"@babel/core": "^7.20.0", "@types/luxon": "^3.6.2", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "depcheck": "^1.4.7", "typescript": "~5.8.3"}}