// App.tsx
import React, {useEffect} from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StatusBar, Platform } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { AuthProvider } from './src/context/AuthContext';
import FlashMessage from "react-native-flash-message";
import {NotificationProvider, useNotification} from "./src/context/NotificationContext";
import * as Notifications from "expo-notifications";
import * as SystemUI from 'expo-system-ui';
import { globalTheme} from "./src/constants/theme";

Notifications.setNotificationHandler({
    handleNotification: async () => ({
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
    }),
});

export default function App() {

    useEffect(() => {
        // Cambiar color barra de navegación de Android (inferior)
        SystemUI.setBackgroundColorAsync(globalTheme.gradient[1]); // color deseado

        // También puedes cambiar el estilo del contenido de la barra de navegación
        // No es necesario con expo-system-ui, pero puedes manejar el StatusBar así:
    }, []);

    // Calcular la altura del StatusBar para el offset
    const getStatusBarHeight = () => {
        if (Platform.OS === 'ios') {
            return 44; // Altura típica del StatusBar en iOS
        } else {
            return StatusBar.currentHeight || 24; // Altura del StatusBar en Android
        }
    };

    const statusBarOffset = getStatusBarHeight() + 10; // +10 para un poco de padding extra

    return (
        <NotificationProvider>
            <GestureHandlerRootView style={{ flex: 1 }}>
                <AuthProvider>
                    <AppNavigator />
                    <FlashMessage
                        position="top"
                        statusBarHeight={statusBarOffset}
                    />
                </AuthProvider>
            </GestureHandlerRootView>
        </NotificationProvider>

    );
}